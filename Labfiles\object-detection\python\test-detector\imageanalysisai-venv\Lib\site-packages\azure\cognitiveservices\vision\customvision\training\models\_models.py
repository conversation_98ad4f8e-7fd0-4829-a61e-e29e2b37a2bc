# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
# --------------------------------------------------------------------------

from msrest.serialization import Model
from msrest.exceptions import HttpOperationError


class BoundingBox(Model):
    """Bounding box that defines a region of an image.

    All required parameters must be populated in order to send to Azure.

    :param left: Required. Coordinate of the left boundary.
    :type left: float
    :param top: Required. Coordinate of the top boundary.
    :type top: float
    :param width: Required. Width.
    :type width: float
    :param height: Required. Height.
    :type height: float
    """

    _validation = {
        'left': {'required': True},
        'top': {'required': True},
        'width': {'required': True},
        'height': {'required': True},
    }

    _attribute_map = {
        'left': {'key': 'left', 'type': 'float'},
        'top': {'key': 'top', 'type': 'float'},
        'width': {'key': 'width', 'type': 'float'},
        'height': {'key': 'height', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(BoundingBox, self).__init__(**kwargs)
        self.left = kwargs.get('left', None)
        self.top = kwargs.get('top', None)
        self.width = kwargs.get('width', None)
        self.height = kwargs.get('height', None)


class CreateProjectOptions(Model):
    """Options used for createProject.

    :param export_model_container_uri: The uri to the Azure Storage container
     that will be used to store exported models.
    :type export_model_container_uri: str
    :param notification_queue_uri: The uri to the Azure Storage queue that
     will be used to send project-related notifications. See <a
     href="https://go.microsoft.com/fwlink/?linkid=2144149">Storage
     notifications</a> documentation for setup and message format.
    :type notification_queue_uri: str
    """

    _attribute_map = {
        'export_model_container_uri': {'key': 'exportModelContainerUri', 'type': 'str'},
        'notification_queue_uri': {'key': 'notificationQueueUri', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(CreateProjectOptions, self).__init__(**kwargs)
        self.export_model_container_uri = kwargs.get('export_model_container_uri', None)
        self.notification_queue_uri = kwargs.get('notification_queue_uri', None)


class CustomBaseModelInfo(Model):
    """CustomBaseModelInfo.

    All required parameters must be populated in order to send to Azure.

    :param project_id: Required. Project Id of the previously trained project
     to be used for current iteration's training.
    :type project_id: str
    :param iteration_id: Required. Iteration Id of the previously trained
     project to be used for current iteration's training.
    :type iteration_id: str
    """

    _validation = {
        'project_id': {'required': True},
        'iteration_id': {'required': True},
    }

    _attribute_map = {
        'project_id': {'key': 'projectId', 'type': 'str'},
        'iteration_id': {'key': 'iterationId', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(CustomBaseModelInfo, self).__init__(**kwargs)
        self.project_id = kwargs.get('project_id', None)
        self.iteration_id = kwargs.get('iteration_id', None)


class CustomVisionError(Model):
    """CustomVisionError.

    All required parameters must be populated in order to send to Azure.

    :param code: Required. The error code. Possible values include: 'NoError',
     'BadRequest', 'BadRequestExceededBatchSize', 'BadRequestNotSupported',
     'BadRequestInvalidIds', 'BadRequestProjectName',
     'BadRequestProjectNameNotUnique', 'BadRequestProjectDescription',
     'BadRequestProjectUnknownDomain',
     'BadRequestProjectUnknownClassification',
     'BadRequestProjectUnsupportedDomainTypeChange',
     'BadRequestProjectUnsupportedExportPlatform',
     'BadRequestProjectImagePreprocessingSettings',
     'BadRequestProjectDuplicated', 'BadRequestIterationName',
     'BadRequestIterationNameNotUnique', 'BadRequestIterationDescription',
     'BadRequestIterationIsNotTrained', 'BadRequestIterationValidationFailed',
     'BadRequestWorkspaceCannotBeModified', 'BadRequestWorkspaceNotDeletable',
     'BadRequestTagName', 'BadRequestTagNameNotUnique',
     'BadRequestTagDescription', 'BadRequestTagType',
     'BadRequestMultipleNegativeTag', 'BadRequestMultipleGeneralProductTag',
     'BadRequestImageTags', 'BadRequestImageRegions',
     'BadRequestNegativeAndRegularTagOnSameImage',
     'BadRequestUnsupportedDomain', 'BadRequestRequiredParamIsNull',
     'BadRequestIterationIsPublished', 'BadRequestInvalidPublishName',
     'BadRequestInvalidPublishTarget', 'BadRequestUnpublishFailed',
     'BadRequestIterationNotPublished', 'BadRequestSubscriptionApi',
     'BadRequestExceedProjectLimit',
     'BadRequestExceedIterationPerProjectLimit',
     'BadRequestExceedTagPerProjectLimit', 'BadRequestExceedTagPerImageLimit',
     'BadRequestExceededQuota', 'BadRequestCannotMigrateProjectWithName',
     'BadRequestNotLimitedTrial', 'BadRequestImageBatch',
     'BadRequestImageStream', 'BadRequestImageUrl', 'BadRequestImageFormat',
     'BadRequestImageSizeBytes', 'BadRequestImageDimensions',
     'BadRequestImageAspectRatio', 'BadRequestImageExceededCount',
     'BadRequestTrainingNotNeeded',
     'BadRequestTrainingNotNeededButTrainingPipelineUpdated',
     'BadRequestTrainingValidationFailed',
     'BadRequestClassificationTrainingValidationFailed',
     'BadRequestMultiClassClassificationTrainingValidationFailed',
     'BadRequestMultiLabelClassificationTrainingValidationFailed',
     'BadRequestDetectionTrainingValidationFailed',
     'BadRequestTrainingAlreadyInProgress',
     'BadRequestDetectionTrainingNotAllowNegativeTag',
     'BadRequestInvalidEmailAddress',
     'BadRequestRetiredDomainNotSupportedForTraining',
     'BadRequestDomainNotSupportedForAdvancedTraining',
     'BadRequestExportPlatformNotSupportedForAdvancedTraining',
     'BadRequestReservedBudgetInHoursNotEnoughForAdvancedTraining',
     'BadRequestCustomBaseModelIterationStatusNotCompleted',
     'BadRequestCustomBaseModelDomainNotCompatible',
     'BadRequestCustomBaseModelArchitectureRetired',
     'BadRequestExportValidationFailed', 'BadRequestExportAlreadyInProgress',
     'BadRequestPredictionIdsMissing', 'BadRequestPredictionIdsExceededCount',
     'BadRequestPredictionTagsExceededCount',
     'BadRequestPredictionResultsExceededCount',
     'BadRequestPredictionInvalidApplicationName',
     'BadRequestPredictionInvalidQueryParameters',
     'BadRequestInvalidImportToken', 'BadRequestExportWhileTraining',
     'BadRequestImageMetadataKey', 'BadRequestImageMetadataValue',
     'BadRequestOperationNotSupported', 'BadRequestInvalidArtifactUri',
     'BadRequestCustomerManagedKeyRevoked', 'BadRequestInvalidUri',
     'BadRequestInvalid', 'UnsupportedMediaType', 'Forbidden', 'ForbiddenUser',
     'ForbiddenUserResource', 'ForbiddenUserSignupDisabled',
     'ForbiddenUserSignupAllowanceExceeded', 'ForbiddenUserDoesNotExist',
     'ForbiddenUserDisabled', 'ForbiddenUserInsufficientCapability',
     'ForbiddenDRModeEnabled', 'ForbiddenInvalid', 'NotFound',
     'NotFoundProject', 'NotFoundProjectDefaultIteration', 'NotFoundIteration',
     'NotFoundIterationPerformance', 'NotFoundTag', 'NotFoundImage',
     'NotFoundDomain', 'NotFoundApimSubscription', 'NotFoundInvalid',
     'Conflict', 'ConflictInvalid', 'ErrorUnknown', 'ErrorIterationCopyFailed',
     'ErrorPreparePerformanceMigrationFailed', 'ErrorProjectInvalidWorkspace',
     'ErrorProjectInvalidPipelineConfiguration', 'ErrorProjectInvalidDomain',
     'ErrorProjectTrainingRequestFailed', 'ErrorProjectImportRequestFailed',
     'ErrorProjectExportRequestFailed', 'ErrorFeaturizationServiceUnavailable',
     'ErrorFeaturizationQueueTimeout', 'ErrorFeaturizationInvalidFeaturizer',
     'ErrorFeaturizationAugmentationUnavailable',
     'ErrorFeaturizationUnrecognizedJob',
     'ErrorFeaturizationAugmentationError', 'ErrorExporterInvalidPlatform',
     'ErrorExporterInvalidFeaturizer', 'ErrorExporterInvalidClassifier',
     'ErrorPredictionServiceUnavailable', 'ErrorPredictionModelNotFound',
     'ErrorPredictionModelNotCached', 'ErrorPrediction',
     'ErrorPredictionStorage', 'ErrorRegionProposal', 'ErrorUnknownBaseModel',
     'ErrorServerTimeOut', 'ErrorInvalid'
    :type code: str or
     ~azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorCodes
    :param message: Required. A message explaining the error reported by the
     service.
    :type message: str
    """

    _validation = {
        'code': {'required': True},
        'message': {'required': True},
    }

    _attribute_map = {
        'code': {'key': 'code', 'type': 'str'},
        'message': {'key': 'message', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(CustomVisionError, self).__init__(**kwargs)
        self.code = kwargs.get('code', None)
        self.message = kwargs.get('message', None)


class CustomVisionErrorException(HttpOperationError):
    """Server responded with exception of type: 'CustomVisionError'.

    :param deserialize: A deserializer
    :param response: Server response to be deserialized.
    """

    def __init__(self, deserialize, response, *args):

        super(CustomVisionErrorException, self).__init__(deserialize, response, 'CustomVisionError', *args)


class Domain(Model):
    """Domains are used as the starting point for your project. Each domain is
    optimized for specific types of images. Domains with compact in their name
    can be exported. For more information visit the <a
    href="https://go.microsoft.com/fwlink/?linkid=2117014">domain
    documentation</a>.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar id: Domain id.
    :vartype id: str
    :ivar name: Name of the domain, describing the types of images used to
     train it.
    :vartype name: str
    :ivar type: Domain type: Classification or ObjectDetection. Possible
     values include: 'Classification', 'ObjectDetection'
    :vartype type: str or
     ~azure.cognitiveservices.vision.customvision.training.models.DomainType
    :ivar exportable: Indicating if the domain is exportable.
    :vartype exportable: bool
    :ivar enabled: Indicating if the domain is enabled.
    :vartype enabled: bool
    :ivar exportable_platforms: Platforms that the domain can be exported to.
    :vartype exportable_platforms: list[str]
    :ivar model_information: Model information.
    :vartype model_information:
     ~azure.cognitiveservices.vision.customvision.training.models.ModelInformation
    """

    _validation = {
        'id': {'readonly': True},
        'name': {'readonly': True},
        'type': {'readonly': True},
        'exportable': {'readonly': True},
        'enabled': {'readonly': True},
        'exportable_platforms': {'readonly': True},
        'model_information': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'type': {'key': 'type', 'type': 'str'},
        'exportable': {'key': 'exportable', 'type': 'bool'},
        'enabled': {'key': 'enabled', 'type': 'bool'},
        'exportable_platforms': {'key': 'exportablePlatforms', 'type': '[str]'},
        'model_information': {'key': 'modelInformation', 'type': 'ModelInformation'},
    }

    def __init__(self, **kwargs):
        super(Domain, self).__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.exportable = None
        self.enabled = None
        self.exportable_platforms = None
        self.model_information = None


class Export(Model):
    """Export.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar platform: Platform of the export. Possible values include: 'CoreML',
     'TensorFlow', 'DockerFile', 'ONNX', 'VAIDK', 'OpenVino'
    :vartype platform: str or
     ~azure.cognitiveservices.vision.customvision.training.models.ExportPlatform
    :ivar status: Status of the export. Possible values include: 'Exporting',
     'Failed', 'Done'
    :vartype status: str or
     ~azure.cognitiveservices.vision.customvision.training.models.ExportStatus
    :ivar download_uri: URI used to download the model. If VNET feature is
     enabled this will be a relative path to be used with GetArtifact,
     otherwise this will be an absolute URI to the resource.
    :vartype download_uri: str
    :ivar flavor: Flavor of the export. These are specializations of the
     export platform.
     Docker platform has valid flavors: Linux, Windows, ARM.
     Tensorflow platform has valid flavors: TensorFlowNormal, TensorFlowLite.
     ONNX platform has valid flavors: ONNX10, ONNX12. Possible values include:
     'Linux', 'Windows', 'ONNX10', 'ONNX12', 'ARM', 'TensorFlowNormal',
     'TensorFlowLite'
    :vartype flavor: str or
     ~azure.cognitiveservices.vision.customvision.training.models.ExportFlavor
    :ivar newer_version_available: Indicates an updated version of the export
     package is available and should be re-exported for the latest changes.
    :vartype newer_version_available: bool
    """

    _validation = {
        'platform': {'readonly': True},
        'status': {'readonly': True},
        'download_uri': {'readonly': True},
        'flavor': {'readonly': True},
        'newer_version_available': {'readonly': True},
    }

    _attribute_map = {
        'platform': {'key': 'platform', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'download_uri': {'key': 'downloadUri', 'type': 'str'},
        'flavor': {'key': 'flavor', 'type': 'str'},
        'newer_version_available': {'key': 'newerVersionAvailable', 'type': 'bool'},
    }

    def __init__(self, **kwargs):
        super(Export, self).__init__(**kwargs)
        self.platform = None
        self.status = None
        self.download_uri = None
        self.flavor = None
        self.newer_version_available = None


class Image(Model):
    """Image model to be sent as JSON.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar id: Id of the image.
    :vartype id: str
    :ivar created: Date the image was created.
    :vartype created: datetime
    :ivar width: Width of the image.
    :vartype width: int
    :ivar height: Height of the image.
    :vartype height: int
    :ivar resized_image_uri: The URI to the (resized) image used for training.
     If VNET feature is enabled this will be a relative path to be used with
     GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype resized_image_uri: str
    :ivar thumbnail_uri: The URI to the thumbnail of the original image. If
     VNET feature is enabled this will be a relative path to be used with
     GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype thumbnail_uri: str
    :ivar original_image_uri: The URI to the original uploaded image. If VNET
     feature is enabled this will be a relative path to be used with
     GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype original_image_uri: str
    :ivar tags: Tags associated with this image.
    :vartype tags:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageTag]
    :ivar regions: Regions associated with this image.
    :vartype regions:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageRegion]
    :ivar metadata: Metadata associated with this image.
    :vartype metadata: dict[str, str]
    """

    _validation = {
        'id': {'readonly': True},
        'created': {'readonly': True},
        'width': {'readonly': True},
        'height': {'readonly': True},
        'resized_image_uri': {'readonly': True},
        'thumbnail_uri': {'readonly': True},
        'original_image_uri': {'readonly': True},
        'tags': {'readonly': True},
        'regions': {'readonly': True},
        'metadata': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'width': {'key': 'width', 'type': 'int'},
        'height': {'key': 'height', 'type': 'int'},
        'resized_image_uri': {'key': 'resizedImageUri', 'type': 'str'},
        'thumbnail_uri': {'key': 'thumbnailUri', 'type': 'str'},
        'original_image_uri': {'key': 'originalImageUri', 'type': 'str'},
        'tags': {'key': 'tags', 'type': '[ImageTag]'},
        'regions': {'key': 'regions', 'type': '[ImageRegion]'},
        'metadata': {'key': 'metadata', 'type': '{str}'},
    }

    def __init__(self, **kwargs):
        super(Image, self).__init__(**kwargs)
        self.id = None
        self.created = None
        self.width = None
        self.height = None
        self.resized_image_uri = None
        self.thumbnail_uri = None
        self.original_image_uri = None
        self.tags = None
        self.regions = None
        self.metadata = None


class ImageCreateResult(Model):
    """ImageCreateResult.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar source_url: Source URL of the image.
    :vartype source_url: str
    :ivar status: Status of the image creation. Possible values include: 'OK',
     'OKDuplicate', 'ErrorSource', 'ErrorImageFormat', 'ErrorImageSize',
     'ErrorStorage', 'ErrorLimitExceed', 'ErrorTagLimitExceed',
     'ErrorRegionLimitExceed', 'ErrorUnknown',
     'ErrorNegativeAndRegularTagOnSameImage', 'ErrorImageDimensions',
     'ErrorInvalidTag'
    :vartype status: str or
     ~azure.cognitiveservices.vision.customvision.training.models.ImageCreateStatus
    :ivar image: The image.
    :vartype image:
     ~azure.cognitiveservices.vision.customvision.training.models.Image
    """

    _validation = {
        'source_url': {'readonly': True},
        'status': {'readonly': True},
        'image': {'readonly': True},
    }

    _attribute_map = {
        'source_url': {'key': 'sourceUrl', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'image': {'key': 'image', 'type': 'Image'},
    }

    def __init__(self, **kwargs):
        super(ImageCreateResult, self).__init__(**kwargs)
        self.source_url = None
        self.status = None
        self.image = None


class ImageCreateSummary(Model):
    """ImageCreateSummary.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar is_batch_successful: True if all of the images in the batch were
     created successfully, otherwise false.
    :vartype is_batch_successful: bool
    :ivar images: List of the image creation results.
    :vartype images:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageCreateResult]
    """

    _validation = {
        'is_batch_successful': {'readonly': True},
        'images': {'readonly': True},
    }

    _attribute_map = {
        'is_batch_successful': {'key': 'isBatchSuccessful', 'type': 'bool'},
        'images': {'key': 'images', 'type': '[ImageCreateResult]'},
    }

    def __init__(self, **kwargs):
        super(ImageCreateSummary, self).__init__(**kwargs)
        self.is_batch_successful = None
        self.images = None


class ImageFileCreateBatch(Model):
    """ImageFileCreateBatch.

    :param images:
    :type images:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageFileCreateEntry]
    :param tag_ids:
    :type tag_ids: list[str]
    :param metadata: The metadata of image. Limited to 10 key-value pairs per
     image. The length of key is limited to 128. The length of value is limited
     to 256.
    :type metadata: dict[str, str]
    """

    _attribute_map = {
        'images': {'key': 'images', 'type': '[ImageFileCreateEntry]'},
        'tag_ids': {'key': 'tagIds', 'type': '[str]'},
        'metadata': {'key': 'metadata', 'type': '{str}'},
    }

    def __init__(self, **kwargs):
        super(ImageFileCreateBatch, self).__init__(**kwargs)
        self.images = kwargs.get('images', None)
        self.tag_ids = kwargs.get('tag_ids', None)
        self.metadata = kwargs.get('metadata', None)


class ImageFileCreateEntry(Model):
    """ImageFileCreateEntry.

    :param name:
    :type name: str
    :param contents:
    :type contents: bytearray
    :param tag_ids:
    :type tag_ids: list[str]
    :param regions:
    :type regions:
     list[~azure.cognitiveservices.vision.customvision.training.models.Region]
    """

    _attribute_map = {
        'name': {'key': 'name', 'type': 'str'},
        'contents': {'key': 'contents', 'type': 'bytearray'},
        'tag_ids': {'key': 'tagIds', 'type': '[str]'},
        'regions': {'key': 'regions', 'type': '[Region]'},
    }

    def __init__(self, **kwargs):
        super(ImageFileCreateEntry, self).__init__(**kwargs)
        self.name = kwargs.get('name', None)
        self.contents = kwargs.get('contents', None)
        self.tag_ids = kwargs.get('tag_ids', None)
        self.regions = kwargs.get('regions', None)


class ImageIdCreateBatch(Model):
    """ImageIdCreateBatch.

    :param images:
    :type images:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageIdCreateEntry]
    :param tag_ids:
    :type tag_ids: list[str]
    :param metadata: The metadata of image. Limited to 10 key-value pairs per
     image. The length of key is limited to 128. The length of value is limited
     to 256.
    :type metadata: dict[str, str]
    """

    _attribute_map = {
        'images': {'key': 'images', 'type': '[ImageIdCreateEntry]'},
        'tag_ids': {'key': 'tagIds', 'type': '[str]'},
        'metadata': {'key': 'metadata', 'type': '{str}'},
    }

    def __init__(self, **kwargs):
        super(ImageIdCreateBatch, self).__init__(**kwargs)
        self.images = kwargs.get('images', None)
        self.tag_ids = kwargs.get('tag_ids', None)
        self.metadata = kwargs.get('metadata', None)


class ImageIdCreateEntry(Model):
    """ImageIdCreateEntry.

    :param id: Id of the image.
    :type id: str
    :param tag_ids:
    :type tag_ids: list[str]
    :param regions:
    :type regions:
     list[~azure.cognitiveservices.vision.customvision.training.models.Region]
    """

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'tag_ids': {'key': 'tagIds', 'type': '[str]'},
        'regions': {'key': 'regions', 'type': '[Region]'},
    }

    def __init__(self, **kwargs):
        super(ImageIdCreateEntry, self).__init__(**kwargs)
        self.id = kwargs.get('id', None)
        self.tag_ids = kwargs.get('tag_ids', None)
        self.regions = kwargs.get('regions', None)


class ImageMetadataUpdateEntry(Model):
    """Entry associating a metadata to an image.

    :param image_id: Id of the image.
    :type image_id: str
    :param status: Status of the metadata update. Possible values include:
     'OK', 'ErrorImageNotFound', 'ErrorLimitExceed', 'ErrorUnknown'
    :type status: str or
     ~azure.cognitiveservices.vision.customvision.training.models.ImageMetadataUpdateStatus
    :param metadata: Metadata of the image.
    :type metadata: dict[str, str]
    """

    _attribute_map = {
        'image_id': {'key': 'imageId', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'metadata': {'key': 'metadata', 'type': '{str}'},
    }

    def __init__(self, **kwargs):
        super(ImageMetadataUpdateEntry, self).__init__(**kwargs)
        self.image_id = kwargs.get('image_id', None)
        self.status = kwargs.get('status', None)
        self.metadata = kwargs.get('metadata', None)


class ImageMetadataUpdateSummary(Model):
    """ImageMetadataUpdateSummary.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar is_batch_successful:
    :vartype is_batch_successful: bool
    :ivar images:
    :vartype images:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageMetadataUpdateEntry]
    """

    _validation = {
        'is_batch_successful': {'readonly': True},
        'images': {'readonly': True},
    }

    _attribute_map = {
        'is_batch_successful': {'key': 'isBatchSuccessful', 'type': 'bool'},
        'images': {'key': 'images', 'type': '[ImageMetadataUpdateEntry]'},
    }

    def __init__(self, **kwargs):
        super(ImageMetadataUpdateSummary, self).__init__(**kwargs)
        self.is_batch_successful = None
        self.images = None


class ImagePerformance(Model):
    """Image performance model.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar predictions:
    :vartype predictions:
     list[~azure.cognitiveservices.vision.customvision.training.models.Prediction]
    :ivar id: Id of the image.
    :vartype id: str
    :ivar created: Date the image was created.
    :vartype created: datetime
    :ivar width: Width of the image.
    :vartype width: int
    :ivar height: Height of the image.
    :vartype height: int
    :ivar image_uri: The URI to the image used for training. If VNET feature
     is enabled this will be a relative path to be used with GetArtifact,
     otherwise this will be an absolute URI to the resource.
    :vartype image_uri: str
    :ivar thumbnail_uri: The URI to the thumbnail of the original image. If
     VNET feature is enabled this will be a relative path to be used with
     GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype thumbnail_uri: str
    :ivar tags: Tags associated with this image.
    :vartype tags:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageTag]
    :ivar regions: Regions associated with this image.
    :vartype regions:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageRegion]
    """

    _validation = {
        'predictions': {'readonly': True},
        'id': {'readonly': True},
        'created': {'readonly': True},
        'width': {'readonly': True},
        'height': {'readonly': True},
        'image_uri': {'readonly': True},
        'thumbnail_uri': {'readonly': True},
        'tags': {'readonly': True},
        'regions': {'readonly': True},
    }

    _attribute_map = {
        'predictions': {'key': 'predictions', 'type': '[Prediction]'},
        'id': {'key': 'id', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'width': {'key': 'width', 'type': 'int'},
        'height': {'key': 'height', 'type': 'int'},
        'image_uri': {'key': 'imageUri', 'type': 'str'},
        'thumbnail_uri': {'key': 'thumbnailUri', 'type': 'str'},
        'tags': {'key': 'tags', 'type': '[ImageTag]'},
        'regions': {'key': 'regions', 'type': '[ImageRegion]'},
    }

    def __init__(self, **kwargs):
        super(ImagePerformance, self).__init__(**kwargs)
        self.predictions = None
        self.id = None
        self.created = None
        self.width = None
        self.height = None
        self.image_uri = None
        self.thumbnail_uri = None
        self.tags = None
        self.regions = None


class ImagePrediction(Model):
    """Result of an image prediction request.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar id: Prediction Id.
    :vartype id: str
    :ivar project: Project Id.
    :vartype project: str
    :ivar iteration: Iteration Id.
    :vartype iteration: str
    :ivar created: Date this prediction was created.
    :vartype created: datetime
    :ivar predictions: List of predictions.
    :vartype predictions:
     list[~azure.cognitiveservices.vision.customvision.training.models.Prediction]
    """

    _validation = {
        'id': {'readonly': True},
        'project': {'readonly': True},
        'iteration': {'readonly': True},
        'created': {'readonly': True},
        'predictions': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'project': {'key': 'project', 'type': 'str'},
        'iteration': {'key': 'iteration', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'predictions': {'key': 'predictions', 'type': '[Prediction]'},
    }

    def __init__(self, **kwargs):
        super(ImagePrediction, self).__init__(**kwargs)
        self.id = None
        self.project = None
        self.iteration = None
        self.created = None
        self.predictions = None


class ImageProcessingSettings(Model):
    """Represents image preprocessing settings used by image augmentation.

    :param augmentation_methods: Gets or sets enabled image transforms. The
     key corresponds to the transform name. If value is set to true, then
     correspondent transform is enabled. Otherwise this transform will not be
     used.
     Augmentation will be uniformly distributed among enabled transforms.
    :type augmentation_methods: dict[str, bool]
    """

    _attribute_map = {
        'augmentation_methods': {'key': 'augmentationMethods', 'type': '{bool}'},
    }

    def __init__(self, **kwargs):
        super(ImageProcessingSettings, self).__init__(**kwargs)
        self.augmentation_methods = kwargs.get('augmentation_methods', None)


class ImageRegion(Model):
    """ImageRegion.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar region_id:
    :vartype region_id: str
    :ivar tag_name:
    :vartype tag_name: str
    :ivar created:
    :vartype created: datetime
    :param tag_id: Required. Id of the tag associated with this region.
    :type tag_id: str
    :param left: Required. Coordinate of the left boundary.
    :type left: float
    :param top: Required. Coordinate of the top boundary.
    :type top: float
    :param width: Required. Width.
    :type width: float
    :param height: Required. Height.
    :type height: float
    """

    _validation = {
        'region_id': {'readonly': True},
        'tag_name': {'readonly': True},
        'created': {'readonly': True},
        'tag_id': {'required': True},
        'left': {'required': True},
        'top': {'required': True},
        'width': {'required': True},
        'height': {'required': True},
    }

    _attribute_map = {
        'region_id': {'key': 'regionId', 'type': 'str'},
        'tag_name': {'key': 'tagName', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'tag_id': {'key': 'tagId', 'type': 'str'},
        'left': {'key': 'left', 'type': 'float'},
        'top': {'key': 'top', 'type': 'float'},
        'width': {'key': 'width', 'type': 'float'},
        'height': {'key': 'height', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(ImageRegion, self).__init__(**kwargs)
        self.region_id = None
        self.tag_name = None
        self.created = None
        self.tag_id = kwargs.get('tag_id', None)
        self.left = kwargs.get('left', None)
        self.top = kwargs.get('top', None)
        self.width = kwargs.get('width', None)
        self.height = kwargs.get('height', None)


class ImageRegionCreateBatch(Model):
    """Batch of image region information to create.

    :param regions:
    :type regions:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageRegionCreateEntry]
    """

    _attribute_map = {
        'regions': {'key': 'regions', 'type': '[ImageRegionCreateEntry]'},
    }

    def __init__(self, **kwargs):
        super(ImageRegionCreateBatch, self).__init__(**kwargs)
        self.regions = kwargs.get('regions', None)


class ImageRegionCreateEntry(Model):
    """Entry associating a region to an image.

    All required parameters must be populated in order to send to Azure.

    :param image_id: Required. Id of the image.
    :type image_id: str
    :param tag_id: Required. Id of the tag associated with this region.
    :type tag_id: str
    :param left: Required. Coordinate of the left boundary.
    :type left: float
    :param top: Required. Coordinate of the top boundary.
    :type top: float
    :param width: Required. Width.
    :type width: float
    :param height: Required. Height.
    :type height: float
    """

    _validation = {
        'image_id': {'required': True},
        'tag_id': {'required': True},
        'left': {'required': True},
        'top': {'required': True},
        'width': {'required': True},
        'height': {'required': True},
    }

    _attribute_map = {
        'image_id': {'key': 'imageId', 'type': 'str'},
        'tag_id': {'key': 'tagId', 'type': 'str'},
        'left': {'key': 'left', 'type': 'float'},
        'top': {'key': 'top', 'type': 'float'},
        'width': {'key': 'width', 'type': 'float'},
        'height': {'key': 'height', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(ImageRegionCreateEntry, self).__init__(**kwargs)
        self.image_id = kwargs.get('image_id', None)
        self.tag_id = kwargs.get('tag_id', None)
        self.left = kwargs.get('left', None)
        self.top = kwargs.get('top', None)
        self.width = kwargs.get('width', None)
        self.height = kwargs.get('height', None)


class ImageRegionCreateResult(Model):
    """ImageRegionCreateResult.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar image_id:
    :vartype image_id: str
    :ivar region_id:
    :vartype region_id: str
    :ivar tag_name:
    :vartype tag_name: str
    :ivar created:
    :vartype created: datetime
    :param tag_id: Required. Id of the tag associated with this region.
    :type tag_id: str
    :param left: Required. Coordinate of the left boundary.
    :type left: float
    :param top: Required. Coordinate of the top boundary.
    :type top: float
    :param width: Required. Width.
    :type width: float
    :param height: Required. Height.
    :type height: float
    """

    _validation = {
        'image_id': {'readonly': True},
        'region_id': {'readonly': True},
        'tag_name': {'readonly': True},
        'created': {'readonly': True},
        'tag_id': {'required': True},
        'left': {'required': True},
        'top': {'required': True},
        'width': {'required': True},
        'height': {'required': True},
    }

    _attribute_map = {
        'image_id': {'key': 'imageId', 'type': 'str'},
        'region_id': {'key': 'regionId', 'type': 'str'},
        'tag_name': {'key': 'tagName', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'tag_id': {'key': 'tagId', 'type': 'str'},
        'left': {'key': 'left', 'type': 'float'},
        'top': {'key': 'top', 'type': 'float'},
        'width': {'key': 'width', 'type': 'float'},
        'height': {'key': 'height', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(ImageRegionCreateResult, self).__init__(**kwargs)
        self.image_id = None
        self.region_id = None
        self.tag_name = None
        self.created = None
        self.tag_id = kwargs.get('tag_id', None)
        self.left = kwargs.get('left', None)
        self.top = kwargs.get('top', None)
        self.width = kwargs.get('width', None)
        self.height = kwargs.get('height', None)


class ImageRegionCreateSummary(Model):
    """ImageRegionCreateSummary.

    :param created:
    :type created:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageRegionCreateResult]
    :param duplicated:
    :type duplicated:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageRegionCreateEntry]
    :param exceeded:
    :type exceeded:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageRegionCreateEntry]
    """

    _attribute_map = {
        'created': {'key': 'created', 'type': '[ImageRegionCreateResult]'},
        'duplicated': {'key': 'duplicated', 'type': '[ImageRegionCreateEntry]'},
        'exceeded': {'key': 'exceeded', 'type': '[ImageRegionCreateEntry]'},
    }

    def __init__(self, **kwargs):
        super(ImageRegionCreateSummary, self).__init__(**kwargs)
        self.created = kwargs.get('created', None)
        self.duplicated = kwargs.get('duplicated', None)
        self.exceeded = kwargs.get('exceeded', None)


class ImageRegionProposal(Model):
    """ImageRegionProposal.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar project_id:
    :vartype project_id: str
    :ivar image_id:
    :vartype image_id: str
    :ivar proposals:
    :vartype proposals:
     list[~azure.cognitiveservices.vision.customvision.training.models.RegionProposal]
    """

    _validation = {
        'project_id': {'readonly': True},
        'image_id': {'readonly': True},
        'proposals': {'readonly': True},
    }

    _attribute_map = {
        'project_id': {'key': 'projectId', 'type': 'str'},
        'image_id': {'key': 'imageId', 'type': 'str'},
        'proposals': {'key': 'proposals', 'type': '[RegionProposal]'},
    }

    def __init__(self, **kwargs):
        super(ImageRegionProposal, self).__init__(**kwargs)
        self.project_id = None
        self.image_id = None
        self.proposals = None


class ImageTag(Model):
    """ImageTag.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar tag_id:
    :vartype tag_id: str
    :ivar tag_name:
    :vartype tag_name: str
    :ivar created:
    :vartype created: datetime
    """

    _validation = {
        'tag_id': {'readonly': True},
        'tag_name': {'readonly': True},
        'created': {'readonly': True},
    }

    _attribute_map = {
        'tag_id': {'key': 'tagId', 'type': 'str'},
        'tag_name': {'key': 'tagName', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
    }

    def __init__(self, **kwargs):
        super(ImageTag, self).__init__(**kwargs)
        self.tag_id = None
        self.tag_name = None
        self.created = None


class ImageTagCreateBatch(Model):
    """Batch of image tags.

    :param tags: Image Tag entries to include in this batch.
    :type tags:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageTagCreateEntry]
    """

    _attribute_map = {
        'tags': {'key': 'tags', 'type': '[ImageTagCreateEntry]'},
    }

    def __init__(self, **kwargs):
        super(ImageTagCreateBatch, self).__init__(**kwargs)
        self.tags = kwargs.get('tags', None)


class ImageTagCreateEntry(Model):
    """Entry associating a tag to an image.

    :param image_id: Id of the image.
    :type image_id: str
    :param tag_id: Id of the tag.
    :type tag_id: str
    """

    _attribute_map = {
        'image_id': {'key': 'imageId', 'type': 'str'},
        'tag_id': {'key': 'tagId', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(ImageTagCreateEntry, self).__init__(**kwargs)
        self.image_id = kwargs.get('image_id', None)
        self.tag_id = kwargs.get('tag_id', None)


class ImageTagCreateSummary(Model):
    """ImageTagCreateSummary.

    :param created:
    :type created:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageTagCreateEntry]
    :param duplicated:
    :type duplicated:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageTagCreateEntry]
    :param exceeded:
    :type exceeded:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageTagCreateEntry]
    """

    _attribute_map = {
        'created': {'key': 'created', 'type': '[ImageTagCreateEntry]'},
        'duplicated': {'key': 'duplicated', 'type': '[ImageTagCreateEntry]'},
        'exceeded': {'key': 'exceeded', 'type': '[ImageTagCreateEntry]'},
    }

    def __init__(self, **kwargs):
        super(ImageTagCreateSummary, self).__init__(**kwargs)
        self.created = kwargs.get('created', None)
        self.duplicated = kwargs.get('duplicated', None)
        self.exceeded = kwargs.get('exceeded', None)


class ImageUrl(Model):
    """Image url.

    All required parameters must be populated in order to send to Azure.

    :param url: Required. Url of the image.
    :type url: str
    """

    _validation = {
        'url': {'required': True},
    }

    _attribute_map = {
        'url': {'key': 'url', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(ImageUrl, self).__init__(**kwargs)
        self.url = kwargs.get('url', None)


class ImageUrlCreateBatch(Model):
    """ImageUrlCreateBatch.

    :param images:
    :type images:
     list[~azure.cognitiveservices.vision.customvision.training.models.ImageUrlCreateEntry]
    :param tag_ids:
    :type tag_ids: list[str]
    :param metadata: The metadata of image. Limited to 10 key-value pairs per
     image. The length of key is limited to 128. The length of value is limited
     to 256.
    :type metadata: dict[str, str]
    """

    _attribute_map = {
        'images': {'key': 'images', 'type': '[ImageUrlCreateEntry]'},
        'tag_ids': {'key': 'tagIds', 'type': '[str]'},
        'metadata': {'key': 'metadata', 'type': '{str}'},
    }

    def __init__(self, **kwargs):
        super(ImageUrlCreateBatch, self).__init__(**kwargs)
        self.images = kwargs.get('images', None)
        self.tag_ids = kwargs.get('tag_ids', None)
        self.metadata = kwargs.get('metadata', None)


class ImageUrlCreateEntry(Model):
    """ImageUrlCreateEntry.

    All required parameters must be populated in order to send to Azure.

    :param url: Required. Url of the image.
    :type url: str
    :param tag_ids:
    :type tag_ids: list[str]
    :param regions:
    :type regions:
     list[~azure.cognitiveservices.vision.customvision.training.models.Region]
    """

    _validation = {
        'url': {'required': True},
    }

    _attribute_map = {
        'url': {'key': 'url', 'type': 'str'},
        'tag_ids': {'key': 'tagIds', 'type': '[str]'},
        'regions': {'key': 'regions', 'type': '[Region]'},
    }

    def __init__(self, **kwargs):
        super(ImageUrlCreateEntry, self).__init__(**kwargs)
        self.url = kwargs.get('url', None)
        self.tag_ids = kwargs.get('tag_ids', None)
        self.regions = kwargs.get('regions', None)


class Iteration(Model):
    """Iteration model to be sent over JSON.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Gets the id of the iteration.
    :vartype id: str
    :param name: Required. Gets or sets the name of the iteration.
    :type name: str
    :ivar status: Gets the current iteration status.
    :vartype status: str
    :ivar created: Gets the time this iteration was completed.
    :vartype created: datetime
    :ivar last_modified: Gets the time this iteration was last modified.
    :vartype last_modified: datetime
    :ivar trained_at: Gets the time this iteration was last modified.
    :vartype trained_at: datetime
    :ivar project_id: Gets the project id of the iteration.
    :vartype project_id: str
    :ivar exportable: Whether the iteration can be exported to another format
     for download.
    :vartype exportable: bool
    :ivar exportable_to: A set of platforms this iteration can export to.
    :vartype exportable_to: list[str]
    :ivar domain_id: Get or sets a guid of the domain the iteration has been
     trained on.
    :vartype domain_id: str
    :ivar classification_type: Gets the classification type of the project.
     Possible values include: 'Multiclass', 'Multilabel'
    :vartype classification_type: str or
     ~azure.cognitiveservices.vision.customvision.training.models.Classifier
    :ivar training_type: Gets the training type of the iteration. Possible
     values include: 'Regular', 'Advanced'
    :vartype training_type: str or
     ~azure.cognitiveservices.vision.customvision.training.models.TrainingType
    :ivar reserved_budget_in_hours: Gets the reserved advanced training budget
     for the iteration.
    :vartype reserved_budget_in_hours: int
    :ivar training_time_in_minutes: Gets the training time for the iteration.
    :vartype training_time_in_minutes: int
    :ivar publish_name: Name of the published model.
    :vartype publish_name: str
    :ivar original_publish_resource_id: Resource Provider Id this iteration
     was originally published to.
    :vartype original_publish_resource_id: str
    :ivar custom_base_model_info: Information of the previously trained
     iteration which provides the base model for current iteration's training.
     Default value of null specifies that no previously trained iteration will
     be used for incremental learning.
    :vartype custom_base_model_info:
     ~azure.cognitiveservices.vision.customvision.training.models.CustomBaseModelInfo
    :ivar training_error_details: Training error details, when training fails.
     Value is null when training succeeds.
    :vartype training_error_details: str
    """

    _validation = {
        'id': {'readonly': True},
        'name': {'required': True},
        'status': {'readonly': True},
        'created': {'readonly': True},
        'last_modified': {'readonly': True},
        'trained_at': {'readonly': True},
        'project_id': {'readonly': True},
        'exportable': {'readonly': True},
        'exportable_to': {'readonly': True},
        'domain_id': {'readonly': True},
        'classification_type': {'readonly': True},
        'training_type': {'readonly': True},
        'reserved_budget_in_hours': {'readonly': True},
        'training_time_in_minutes': {'readonly': True},
        'publish_name': {'readonly': True},
        'original_publish_resource_id': {'readonly': True},
        'custom_base_model_info': {'readonly': True},
        'training_error_details': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'status': {'key': 'status', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'last_modified': {'key': 'lastModified', 'type': 'iso-8601'},
        'trained_at': {'key': 'trainedAt', 'type': 'iso-8601'},
        'project_id': {'key': 'projectId', 'type': 'str'},
        'exportable': {'key': 'exportable', 'type': 'bool'},
        'exportable_to': {'key': 'exportableTo', 'type': '[str]'},
        'domain_id': {'key': 'domainId', 'type': 'str'},
        'classification_type': {'key': 'classificationType', 'type': 'str'},
        'training_type': {'key': 'trainingType', 'type': 'str'},
        'reserved_budget_in_hours': {'key': 'reservedBudgetInHours', 'type': 'int'},
        'training_time_in_minutes': {'key': 'trainingTimeInMinutes', 'type': 'int'},
        'publish_name': {'key': 'publishName', 'type': 'str'},
        'original_publish_resource_id': {'key': 'originalPublishResourceId', 'type': 'str'},
        'custom_base_model_info': {'key': 'customBaseModelInfo', 'type': 'CustomBaseModelInfo'},
        'training_error_details': {'key': 'trainingErrorDetails', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(Iteration, self).__init__(**kwargs)
        self.id = None
        self.name = kwargs.get('name', None)
        self.status = None
        self.created = None
        self.last_modified = None
        self.trained_at = None
        self.project_id = None
        self.exportable = None
        self.exportable_to = None
        self.domain_id = None
        self.classification_type = None
        self.training_type = None
        self.reserved_budget_in_hours = None
        self.training_time_in_minutes = None
        self.publish_name = None
        self.original_publish_resource_id = None
        self.custom_base_model_info = None
        self.training_error_details = None


class IterationPerformance(Model):
    """Represents the detailed performance data for a trained iteration.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar per_tag_performance: Gets the per-tag performance details for this
     iteration.
    :vartype per_tag_performance:
     list[~azure.cognitiveservices.vision.customvision.training.models.TagPerformance]
    :ivar precision: Gets the precision.
    :vartype precision: float
    :ivar precision_std_deviation: Gets the standard deviation for the
     precision.
    :vartype precision_std_deviation: float
    :ivar recall: Gets the recall.
    :vartype recall: float
    :ivar recall_std_deviation: Gets the standard deviation for the recall.
    :vartype recall_std_deviation: float
    :ivar average_precision: Gets the average precision when applicable.
    :vartype average_precision: float
    """

    _validation = {
        'per_tag_performance': {'readonly': True},
        'precision': {'readonly': True},
        'precision_std_deviation': {'readonly': True},
        'recall': {'readonly': True},
        'recall_std_deviation': {'readonly': True},
        'average_precision': {'readonly': True},
    }

    _attribute_map = {
        'per_tag_performance': {'key': 'perTagPerformance', 'type': '[TagPerformance]'},
        'precision': {'key': 'precision', 'type': 'float'},
        'precision_std_deviation': {'key': 'precisionStdDeviation', 'type': 'float'},
        'recall': {'key': 'recall', 'type': 'float'},
        'recall_std_deviation': {'key': 'recallStdDeviation', 'type': 'float'},
        'average_precision': {'key': 'averagePrecision', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(IterationPerformance, self).__init__(**kwargs)
        self.per_tag_performance = None
        self.precision = None
        self.precision_std_deviation = None
        self.recall = None
        self.recall_std_deviation = None
        self.average_precision = None


class ModelInformation(Model):
    """Model information.

    All required parameters must be populated in order to send to Azure.

    :param estimated_model_size_in_megabytes: Estimation of the exported FP32
     Onnx model size (2 tags) in megabytes. This information is not present if
     the model cannot be exported.
    :type estimated_model_size_in_megabytes: int
    :param description: Required. Model description.
    :type description: str
    """

    _validation = {
        'description': {'required': True},
    }

    _attribute_map = {
        'estimated_model_size_in_megabytes': {'key': 'estimatedModelSizeInMegabytes', 'type': 'int'},
        'description': {'key': 'description', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(ModelInformation, self).__init__(**kwargs)
        self.estimated_model_size_in_megabytes = kwargs.get('estimated_model_size_in_megabytes', None)
        self.description = kwargs.get('description', None)


class Prediction(Model):
    """Prediction result.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar probability: Probability of the tag.
    :vartype probability: float
    :ivar tag_id: Id of the predicted tag.
    :vartype tag_id: str
    :ivar tag_name: Name of the predicted tag.
    :vartype tag_name: str
    :ivar bounding_box: Bounding box of the prediction.
    :vartype bounding_box:
     ~azure.cognitiveservices.vision.customvision.training.models.BoundingBox
    :ivar tag_type: Type of the predicted tag. Possible values include:
     'Regular', 'Negative', 'GeneralProduct'
    :vartype tag_type: str or
     ~azure.cognitiveservices.vision.customvision.training.models.TagType
    """

    _validation = {
        'probability': {'readonly': True},
        'tag_id': {'readonly': True},
        'tag_name': {'readonly': True},
        'bounding_box': {'readonly': True},
        'tag_type': {'readonly': True},
    }

    _attribute_map = {
        'probability': {'key': 'probability', 'type': 'float'},
        'tag_id': {'key': 'tagId', 'type': 'str'},
        'tag_name': {'key': 'tagName', 'type': 'str'},
        'bounding_box': {'key': 'boundingBox', 'type': 'BoundingBox'},
        'tag_type': {'key': 'tagType', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(Prediction, self).__init__(**kwargs)
        self.probability = None
        self.tag_id = None
        self.tag_name = None
        self.bounding_box = None
        self.tag_type = None


class PredictionQueryResult(Model):
    """Query result of the prediction images that were sent to your prediction
    endpoint.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :param token: Prediction Query Token.
    :type token:
     ~azure.cognitiveservices.vision.customvision.training.models.PredictionQueryToken
    :ivar results: Result of an image prediction request.
    :vartype results:
     list[~azure.cognitiveservices.vision.customvision.training.models.StoredImagePrediction]
    """

    _validation = {
        'results': {'readonly': True},
    }

    _attribute_map = {
        'token': {'key': 'token', 'type': 'PredictionQueryToken'},
        'results': {'key': 'results', 'type': '[StoredImagePrediction]'},
    }

    def __init__(self, **kwargs):
        super(PredictionQueryResult, self).__init__(**kwargs)
        self.token = kwargs.get('token', None)
        self.results = None


class PredictionQueryTag(Model):
    """PredictionQueryTag.

    :param id:
    :type id: str
    :param min_threshold:
    :type min_threshold: float
    :param max_threshold:
    :type max_threshold: float
    """

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'min_threshold': {'key': 'minThreshold', 'type': 'float'},
        'max_threshold': {'key': 'maxThreshold', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(PredictionQueryTag, self).__init__(**kwargs)
        self.id = kwargs.get('id', None)
        self.min_threshold = kwargs.get('min_threshold', None)
        self.max_threshold = kwargs.get('max_threshold', None)


class PredictionQueryToken(Model):
    """PredictionQueryToken.

    :param session:
    :type session: str
    :param continuation:
    :type continuation: str
    :param max_count:
    :type max_count: int
    :param order_by: Possible values include: 'Newest', 'Oldest', 'Suggested'
    :type order_by: str or
     ~azure.cognitiveservices.vision.customvision.training.models.OrderBy
    :param tags:
    :type tags:
     list[~azure.cognitiveservices.vision.customvision.training.models.PredictionQueryTag]
    :param iteration_id:
    :type iteration_id: str
    :param start_time:
    :type start_time: datetime
    :param end_time:
    :type end_time: datetime
    :param application:
    :type application: str
    """

    _attribute_map = {
        'session': {'key': 'session', 'type': 'str'},
        'continuation': {'key': 'continuation', 'type': 'str'},
        'max_count': {'key': 'maxCount', 'type': 'int'},
        'order_by': {'key': 'orderBy', 'type': 'str'},
        'tags': {'key': 'tags', 'type': '[PredictionQueryTag]'},
        'iteration_id': {'key': 'iterationId', 'type': 'str'},
        'start_time': {'key': 'startTime', 'type': 'iso-8601'},
        'end_time': {'key': 'endTime', 'type': 'iso-8601'},
        'application': {'key': 'application', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(PredictionQueryToken, self).__init__(**kwargs)
        self.session = kwargs.get('session', None)
        self.continuation = kwargs.get('continuation', None)
        self.max_count = kwargs.get('max_count', None)
        self.order_by = kwargs.get('order_by', None)
        self.tags = kwargs.get('tags', None)
        self.iteration_id = kwargs.get('iteration_id', None)
        self.start_time = kwargs.get('start_time', None)
        self.end_time = kwargs.get('end_time', None)
        self.application = kwargs.get('application', None)


class Project(Model):
    """Represents a project.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Gets the project id.
    :vartype id: str
    :param name: Required. Gets or sets the name of the project.
    :type name: str
    :param description: Required. Gets or sets the description of the project.
    :type description: str
    :param settings: Required. Gets or sets the project settings.
    :type settings:
     ~azure.cognitiveservices.vision.customvision.training.models.ProjectSettings
    :ivar created: Gets the date this project was created.
    :vartype created: datetime
    :ivar last_modified: Gets the date this project was last modified.
    :vartype last_modified: datetime
    :ivar thumbnail_uri: Gets the thumbnail url representing the image. If
     VNET feature is enabled this will be a relative path to be used with
     GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype thumbnail_uri: str
    :ivar dr_mode_enabled: Gets if the Disaster Recovery (DR) mode is on,
     indicating the project is temporarily read-only.
    :vartype dr_mode_enabled: bool
    :param status: Gets the status of the project. Possible values include:
     'Succeeded', 'Importing', 'Failed'
    :type status: str or
     ~azure.cognitiveservices.vision.customvision.training.models.ProjectStatus
    """

    _validation = {
        'id': {'readonly': True},
        'name': {'required': True},
        'description': {'required': True},
        'settings': {'required': True},
        'created': {'readonly': True},
        'last_modified': {'readonly': True},
        'thumbnail_uri': {'readonly': True},
        'dr_mode_enabled': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'description': {'key': 'description', 'type': 'str'},
        'settings': {'key': 'settings', 'type': 'ProjectSettings'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'last_modified': {'key': 'lastModified', 'type': 'iso-8601'},
        'thumbnail_uri': {'key': 'thumbnailUri', 'type': 'str'},
        'dr_mode_enabled': {'key': 'drModeEnabled', 'type': 'bool'},
        'status': {'key': 'status', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(Project, self).__init__(**kwargs)
        self.id = None
        self.name = kwargs.get('name', None)
        self.description = kwargs.get('description', None)
        self.settings = kwargs.get('settings', None)
        self.created = None
        self.last_modified = None
        self.thumbnail_uri = None
        self.dr_mode_enabled = None
        self.status = kwargs.get('status', None)


class ProjectExport(Model):
    """Represents information about a project export.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar iteration_count: Count of iterations that will be exported.
    :vartype iteration_count: int
    :ivar image_count: Count of images that will be exported.
    :vartype image_count: int
    :ivar tag_count: Count of tags that will be exported.
    :vartype tag_count: int
    :ivar region_count: Count of regions that will be exported.
    :vartype region_count: int
    :ivar estimated_import_time_in_ms: Estimated time this project will take
     to import, can change based on network connectivity and load between
     source and destination regions.
    :vartype estimated_import_time_in_ms: int
    :ivar token: Opaque token that should be passed to ImportProject to
     perform the import. This token grants access to import this
     project to all that have the token.
    :vartype token: str
    """

    _validation = {
        'iteration_count': {'readonly': True},
        'image_count': {'readonly': True},
        'tag_count': {'readonly': True},
        'region_count': {'readonly': True},
        'estimated_import_time_in_ms': {'readonly': True},
        'token': {'readonly': True},
    }

    _attribute_map = {
        'iteration_count': {'key': 'iterationCount', 'type': 'int'},
        'image_count': {'key': 'imageCount', 'type': 'int'},
        'tag_count': {'key': 'tagCount', 'type': 'int'},
        'region_count': {'key': 'regionCount', 'type': 'int'},
        'estimated_import_time_in_ms': {'key': 'estimatedImportTimeInMS', 'type': 'int'},
        'token': {'key': 'token', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(ProjectExport, self).__init__(**kwargs)
        self.iteration_count = None
        self.image_count = None
        self.tag_count = None
        self.region_count = None
        self.estimated_import_time_in_ms = None
        self.token = None


class ProjectSettings(Model):
    """Represents settings associated with a project.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :param domain_id: Gets or sets the id of the Domain to use with this
     project.
    :type domain_id: str
    :param classification_type: Gets or sets the classification type of the
     project. Possible values include: 'Multiclass', 'Multilabel'
    :type classification_type: str or
     ~azure.cognitiveservices.vision.customvision.training.models.Classifier
    :param target_export_platforms: A list of ExportPlatform that the trained
     model should be able to support.
    :type target_export_platforms: list[str]
    :ivar use_negative_set: Indicates if negative set is being used.
    :vartype use_negative_set: bool
    :ivar detection_parameters: Detection parameters in use, if any.
    :vartype detection_parameters: str
    :param image_processing_settings: Gets or sets image preprocessing
     settings.
    :type image_processing_settings:
     ~azure.cognitiveservices.vision.customvision.training.models.ImageProcessingSettings
    :ivar export_model_container_uri: The uri to the Azure Storage container
     that will be used to store exported models.
    :vartype export_model_container_uri: str
    :ivar notification_queue_uri: The uri to the Azure Storage queue that will
     be used to send project-related notifications. See <a
     href="https://go.microsoft.com/fwlink/?linkid=2144149">Storage
     notifications</a> documentation for setup and message format.
    :vartype notification_queue_uri: str
    """

    _validation = {
        'use_negative_set': {'readonly': True},
        'detection_parameters': {'readonly': True},
        'export_model_container_uri': {'readonly': True},
        'notification_queue_uri': {'readonly': True},
    }

    _attribute_map = {
        'domain_id': {'key': 'domainId', 'type': 'str'},
        'classification_type': {'key': 'classificationType', 'type': 'str'},
        'target_export_platforms': {'key': 'targetExportPlatforms', 'type': '[str]'},
        'use_negative_set': {'key': 'useNegativeSet', 'type': 'bool'},
        'detection_parameters': {'key': 'detectionParameters', 'type': 'str'},
        'image_processing_settings': {'key': 'imageProcessingSettings', 'type': 'ImageProcessingSettings'},
        'export_model_container_uri': {'key': 'exportModelContainerUri', 'type': 'str'},
        'notification_queue_uri': {'key': 'notificationQueueUri', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(ProjectSettings, self).__init__(**kwargs)
        self.domain_id = kwargs.get('domain_id', None)
        self.classification_type = kwargs.get('classification_type', None)
        self.target_export_platforms = kwargs.get('target_export_platforms', None)
        self.use_negative_set = None
        self.detection_parameters = None
        self.image_processing_settings = kwargs.get('image_processing_settings', None)
        self.export_model_container_uri = None
        self.notification_queue_uri = None


class Region(Model):
    """Region.

    All required parameters must be populated in order to send to Azure.

    :param tag_id: Required. Id of the tag associated with this region.
    :type tag_id: str
    :param left: Required. Coordinate of the left boundary.
    :type left: float
    :param top: Required. Coordinate of the top boundary.
    :type top: float
    :param width: Required. Width.
    :type width: float
    :param height: Required. Height.
    :type height: float
    """

    _validation = {
        'tag_id': {'required': True},
        'left': {'required': True},
        'top': {'required': True},
        'width': {'required': True},
        'height': {'required': True},
    }

    _attribute_map = {
        'tag_id': {'key': 'tagId', 'type': 'str'},
        'left': {'key': 'left', 'type': 'float'},
        'top': {'key': 'top', 'type': 'float'},
        'width': {'key': 'width', 'type': 'float'},
        'height': {'key': 'height', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(Region, self).__init__(**kwargs)
        self.tag_id = kwargs.get('tag_id', None)
        self.left = kwargs.get('left', None)
        self.top = kwargs.get('top', None)
        self.width = kwargs.get('width', None)
        self.height = kwargs.get('height', None)


class RegionProposal(Model):
    """RegionProposal.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar confidence:
    :vartype confidence: float
    :ivar bounding_box:
    :vartype bounding_box:
     ~azure.cognitiveservices.vision.customvision.training.models.BoundingBox
    """

    _validation = {
        'confidence': {'readonly': True},
        'bounding_box': {'readonly': True},
    }

    _attribute_map = {
        'confidence': {'key': 'confidence', 'type': 'float'},
        'bounding_box': {'key': 'boundingBox', 'type': 'BoundingBox'},
    }

    def __init__(self, **kwargs):
        super(RegionProposal, self).__init__(**kwargs)
        self.confidence = None
        self.bounding_box = None


class StoredImagePrediction(Model):
    """Result of an image prediction request.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar resized_image_uri: The URI to the (resized) prediction image. If
     VNET feature is enabled this will be a relative path to be used with
     GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype resized_image_uri: str
    :ivar thumbnail_uri: The URI to the thumbnail of the original prediction
     image. If VNET feature is enabled this will be a relative path to be used
     with GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype thumbnail_uri: str
    :ivar original_image_uri: The URI to the original prediction image. If
     VNET feature is enabled this will be a relative path to be used with
     GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype original_image_uri: str
    :ivar domain: Domain used for the prediction.
    :vartype domain: str
    :ivar id: Prediction Id.
    :vartype id: str
    :ivar project: Project Id.
    :vartype project: str
    :ivar iteration: Iteration Id.
    :vartype iteration: str
    :ivar created: Date this prediction was created.
    :vartype created: datetime
    :ivar predictions: List of predictions.
    :vartype predictions:
     list[~azure.cognitiveservices.vision.customvision.training.models.Prediction]
    """

    _validation = {
        'resized_image_uri': {'readonly': True},
        'thumbnail_uri': {'readonly': True},
        'original_image_uri': {'readonly': True},
        'domain': {'readonly': True},
        'id': {'readonly': True},
        'project': {'readonly': True},
        'iteration': {'readonly': True},
        'created': {'readonly': True},
        'predictions': {'readonly': True},
    }

    _attribute_map = {
        'resized_image_uri': {'key': 'resizedImageUri', 'type': 'str'},
        'thumbnail_uri': {'key': 'thumbnailUri', 'type': 'str'},
        'original_image_uri': {'key': 'originalImageUri', 'type': 'str'},
        'domain': {'key': 'domain', 'type': 'str'},
        'id': {'key': 'id', 'type': 'str'},
        'project': {'key': 'project', 'type': 'str'},
        'iteration': {'key': 'iteration', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'predictions': {'key': 'predictions', 'type': '[Prediction]'},
    }

    def __init__(self, **kwargs):
        super(StoredImagePrediction, self).__init__(**kwargs)
        self.resized_image_uri = None
        self.thumbnail_uri = None
        self.original_image_uri = None
        self.domain = None
        self.id = None
        self.project = None
        self.iteration = None
        self.created = None
        self.predictions = None


class StoredSuggestedTagAndRegion(Model):
    """Result of a suggested tags and regions request of the untagged image.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar width: Width of the resized image.
    :vartype width: int
    :ivar height: Height of the resized image.
    :vartype height: int
    :ivar resized_image_uri: The URI to the (resized) prediction image. If
     VNET feature is enabled this will be a relative path to be used with
     GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype resized_image_uri: str
    :ivar thumbnail_uri: The URI to the thumbnail of the original prediction
     image. If VNET feature is enabled this will be a relative path to be used
     with GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype thumbnail_uri: str
    :ivar original_image_uri: The URI to the original prediction image. If
     VNET feature is enabled this will be a relative path to be used with
     GetArtifact, otherwise this will be an absolute URI to the resource.
    :vartype original_image_uri: str
    :ivar domain: Domain used for the prediction.
    :vartype domain: str
    :ivar id: Prediction Id.
    :vartype id: str
    :ivar project: Project Id.
    :vartype project: str
    :ivar iteration: Iteration Id.
    :vartype iteration: str
    :ivar created: Date this prediction was created.
    :vartype created: datetime
    :ivar predictions: List of predictions.
    :vartype predictions:
     list[~azure.cognitiveservices.vision.customvision.training.models.Prediction]
    :ivar prediction_uncertainty: Uncertainty (entropy) of suggested tags or
     regions per image.
    :vartype prediction_uncertainty: float
    """

    _validation = {
        'width': {'readonly': True},
        'height': {'readonly': True},
        'resized_image_uri': {'readonly': True},
        'thumbnail_uri': {'readonly': True},
        'original_image_uri': {'readonly': True},
        'domain': {'readonly': True},
        'id': {'readonly': True},
        'project': {'readonly': True},
        'iteration': {'readonly': True},
        'created': {'readonly': True},
        'predictions': {'readonly': True},
        'prediction_uncertainty': {'readonly': True},
    }

    _attribute_map = {
        'width': {'key': 'width', 'type': 'int'},
        'height': {'key': 'height', 'type': 'int'},
        'resized_image_uri': {'key': 'resizedImageUri', 'type': 'str'},
        'thumbnail_uri': {'key': 'thumbnailUri', 'type': 'str'},
        'original_image_uri': {'key': 'originalImageUri', 'type': 'str'},
        'domain': {'key': 'domain', 'type': 'str'},
        'id': {'key': 'id', 'type': 'str'},
        'project': {'key': 'project', 'type': 'str'},
        'iteration': {'key': 'iteration', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'predictions': {'key': 'predictions', 'type': '[Prediction]'},
        'prediction_uncertainty': {'key': 'predictionUncertainty', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(StoredSuggestedTagAndRegion, self).__init__(**kwargs)
        self.width = None
        self.height = None
        self.resized_image_uri = None
        self.thumbnail_uri = None
        self.original_image_uri = None
        self.domain = None
        self.id = None
        self.project = None
        self.iteration = None
        self.created = None
        self.predictions = None
        self.prediction_uncertainty = None


class SuggestedTagAndRegion(Model):
    """Result of a suggested tags and regions request.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar id: Prediction Id.
    :vartype id: str
    :ivar project: Project Id.
    :vartype project: str
    :ivar iteration: Iteration Id.
    :vartype iteration: str
    :ivar created: Date this prediction was created.
    :vartype created: datetime
    :ivar predictions: List of predictions.
    :vartype predictions:
     list[~azure.cognitiveservices.vision.customvision.training.models.Prediction]
    :ivar prediction_uncertainty: Uncertainty (entropy) of suggested tags or
     regions per image.
    :vartype prediction_uncertainty: float
    """

    _validation = {
        'id': {'readonly': True},
        'project': {'readonly': True},
        'iteration': {'readonly': True},
        'created': {'readonly': True},
        'predictions': {'readonly': True},
        'prediction_uncertainty': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'project': {'key': 'project', 'type': 'str'},
        'iteration': {'key': 'iteration', 'type': 'str'},
        'created': {'key': 'created', 'type': 'iso-8601'},
        'predictions': {'key': 'predictions', 'type': '[Prediction]'},
        'prediction_uncertainty': {'key': 'predictionUncertainty', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(SuggestedTagAndRegion, self).__init__(**kwargs)
        self.id = None
        self.project = None
        self.iteration = None
        self.created = None
        self.predictions = None
        self.prediction_uncertainty = None


class SuggestedTagAndRegionQuery(Model):
    """The array of result images and token containing session and continuation
    Ids for the next query.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :param token: Contains properties we need to fetch suggested tags for.
    :type token:
     ~azure.cognitiveservices.vision.customvision.training.models.SuggestedTagAndRegionQueryToken
    :ivar results: Result of a suggested tags and regions request of the
     untagged image.
    :vartype results:
     list[~azure.cognitiveservices.vision.customvision.training.models.StoredSuggestedTagAndRegion]
    """

    _validation = {
        'results': {'readonly': True},
    }

    _attribute_map = {
        'token': {'key': 'token', 'type': 'SuggestedTagAndRegionQueryToken'},
        'results': {'key': 'results', 'type': '[StoredSuggestedTagAndRegion]'},
    }

    def __init__(self, **kwargs):
        super(SuggestedTagAndRegionQuery, self).__init__(**kwargs)
        self.token = kwargs.get('token', None)
        self.results = None


class SuggestedTagAndRegionQueryToken(Model):
    """Contains properties we need to fetch suggested tags for. For the first
    call, Session and continuation set to null.
    Then on subsequent calls, uses the session/continuation from the previous
    SuggestedTagAndRegionQuery result to fetch additional results.

    :param tag_ids: Existing TagIds in project to filter suggested tags on.
    :type tag_ids: list[str]
    :param threshold: Confidence threshold to filter suggested tags on.
    :type threshold: float
    :param session: SessionId for database query. Initially set to null but
     later used to paginate.
    :type session: str
    :param continuation: Continuation Id for database pagination. Initially
     null but later used to paginate.
    :type continuation: str
    :param max_count: Maximum number of results you want to be returned in the
     response.
    :type max_count: int
    :param sort_by: OrderBy. Ordering mechanism for your results. Possible
     values include: 'UncertaintyAscending', 'UncertaintyDescending'
    :type sort_by: str or
     ~azure.cognitiveservices.vision.customvision.training.models.SortBy
    """

    _attribute_map = {
        'tag_ids': {'key': 'tagIds', 'type': '[str]'},
        'threshold': {'key': 'threshold', 'type': 'float'},
        'session': {'key': 'session', 'type': 'str'},
        'continuation': {'key': 'continuation', 'type': 'str'},
        'max_count': {'key': 'maxCount', 'type': 'int'},
        'sort_by': {'key': 'sortBy', 'type': 'str'},
    }

    def __init__(self, **kwargs):
        super(SuggestedTagAndRegionQueryToken, self).__init__(**kwargs)
        self.tag_ids = kwargs.get('tag_ids', None)
        self.threshold = kwargs.get('threshold', None)
        self.session = kwargs.get('session', None)
        self.continuation = kwargs.get('continuation', None)
        self.max_count = kwargs.get('max_count', None)
        self.sort_by = kwargs.get('sort_by', None)


class Tag(Model):
    """Represents a Tag.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Gets the Tag ID.
    :vartype id: str
    :param name: Required. Gets or sets the name of the tag.
    :type name: str
    :param description: Required. Gets or sets the description of the tag.
    :type description: str
    :param type: Required. Gets or sets the type of the tag. Possible values
     include: 'Regular', 'Negative', 'GeneralProduct'
    :type type: str or
     ~azure.cognitiveservices.vision.customvision.training.models.TagType
    :ivar image_count: Gets the number of images with this tag.
    :vartype image_count: int
    """

    _validation = {
        'id': {'readonly': True},
        'name': {'required': True},
        'description': {'required': True},
        'type': {'required': True},
        'image_count': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'description': {'key': 'description', 'type': 'str'},
        'type': {'key': 'type', 'type': 'str'},
        'image_count': {'key': 'imageCount', 'type': 'int'},
    }

    def __init__(self, **kwargs):
        super(Tag, self).__init__(**kwargs)
        self.id = None
        self.name = kwargs.get('name', None)
        self.description = kwargs.get('description', None)
        self.type = kwargs.get('type', None)
        self.image_count = None


class TagFilter(Model):
    """Model that query for counting of images whose suggested tags match given
    tags and their probability are greater than or equal to the given
    threshold.

    :param tag_ids: Existing TagIds in project to get suggested tags count
     for.
    :type tag_ids: list[str]
    :param threshold: Confidence threshold to filter suggested tags on.
    :type threshold: float
    """

    _attribute_map = {
        'tag_ids': {'key': 'tagIds', 'type': '[str]'},
        'threshold': {'key': 'threshold', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(TagFilter, self).__init__(**kwargs)
        self.tag_ids = kwargs.get('tag_ids', None)
        self.threshold = kwargs.get('threshold', None)


class TagPerformance(Model):
    """Represents performance data for a particular tag in a trained iteration.

    Variables are only populated by the server, and will be ignored when
    sending a request.

    :ivar id:
    :vartype id: str
    :ivar name:
    :vartype name: str
    :ivar precision: Gets the precision.
    :vartype precision: float
    :ivar precision_std_deviation: Gets the standard deviation for the
     precision.
    :vartype precision_std_deviation: float
    :ivar recall: Gets the recall.
    :vartype recall: float
    :ivar recall_std_deviation: Gets the standard deviation for the recall.
    :vartype recall_std_deviation: float
    :ivar average_precision: Gets the average precision when applicable.
    :vartype average_precision: float
    """

    _validation = {
        'id': {'readonly': True},
        'name': {'readonly': True},
        'precision': {'readonly': True},
        'precision_std_deviation': {'readonly': True},
        'recall': {'readonly': True},
        'recall_std_deviation': {'readonly': True},
        'average_precision': {'readonly': True},
    }

    _attribute_map = {
        'id': {'key': 'id', 'type': 'str'},
        'name': {'key': 'name', 'type': 'str'},
        'precision': {'key': 'precision', 'type': 'float'},
        'precision_std_deviation': {'key': 'precisionStdDeviation', 'type': 'float'},
        'recall': {'key': 'recall', 'type': 'float'},
        'recall_std_deviation': {'key': 'recallStdDeviation', 'type': 'float'},
        'average_precision': {'key': 'averagePrecision', 'type': 'float'},
    }

    def __init__(self, **kwargs):
        super(TagPerformance, self).__init__(**kwargs)
        self.id = None
        self.name = None
        self.precision = None
        self.precision_std_deviation = None
        self.recall = None
        self.recall_std_deviation = None
        self.average_precision = None


class TrainingParameters(Model):
    """Parameters used for training.

    :param selected_tags: List of tags selected for this training session,
     other tags in the project will be ignored.
    :type selected_tags: list[str]
    :param custom_base_model_info: Information of the previously trained
     iteration which provides the base model for current iteration's training.
    :type custom_base_model_info:
     ~azure.cognitiveservices.vision.customvision.training.models.CustomBaseModelInfo
    """

    _attribute_map = {
        'selected_tags': {'key': 'selectedTags', 'type': '[str]'},
        'custom_base_model_info': {'key': 'customBaseModelInfo', 'type': 'CustomBaseModelInfo'},
    }

    def __init__(self, **kwargs):
        super(TrainingParameters, self).__init__(**kwargs)
        self.selected_tags = kwargs.get('selected_tags', None)
        self.custom_base_model_info = kwargs.get('custom_base_model_info', None)
