from azure.ai.vision.face import FaceClient
from azure.ai.vision.face.models import FaceDetectionModel, FaceRecognitionModel, FaceAttributeTypeDetection01
from azure.core.credentials import AzureKeyCredential
from dotenv import load_dotenv
import os

def main():
    print("Starting simplified face test...")
    
    try:
        # Load environment
        load_dotenv()
        endpoint = os.getenv('AI_SERVICE_ENDPOINT')
        key = os.getenv('AI_SERVICE_KEY')
        print(f"Endpoint: {endpoint}")
        print(f"Key: {key[:10]}...")
        
        # Create client
        face_client = FaceClient(
            endpoint=endpoint,
            credential=AzureKeyCredential(key)
        )
        print("✓ Face client created")
        
        # Load image
        image_file = 'images/face1.jpg'
        print(f"Loading image: {image_file}")
        
        with open(image_file, "rb") as f:
            image_data = f.read()
        print(f"✓ Image loaded, size: {len(image_data)} bytes")
        
        # Simple detection first
        print("Attempting simple face detection...")
        detected_faces = face_client.detect(
            image_data,
            detection_model=FaceDetectionModel.DETECTION_03,
            recognition_model=FaceRecognitionModel.RECOGNITION_04,
            return_face_id=True
        )
        
        print(f"✓ Detection successful! Found {len(detected_faces)} faces")
        
        for i, face in enumerate(detected_faces):
            print(f"Face {i+1}: ID = {face.face_id}")
            if face.face_rectangle:
                rect = face.face_rectangle
                print(f"  Rectangle: ({rect.left}, {rect.top}, {rect.width}, {rect.height})")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
