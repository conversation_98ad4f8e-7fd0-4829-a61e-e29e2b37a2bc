# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
# --------------------------------------------------------------------------

from enum import Enum


class CustomVisionErrorCodes(str, Enum):

    no_error = "NoError"
    bad_request = "BadRequest"
    bad_request_exceeded_batch_size = "BadRequestExceededBatchSize"
    bad_request_not_supported = "BadRequestNotSupported"
    bad_request_invalid_ids = "BadRequestInvalidIds"
    bad_request_project_name = "BadRequestProjectName"
    bad_request_project_name_not_unique = "BadRequestProjectNameNotUnique"
    bad_request_project_description = "BadRequestProjectDescription"
    bad_request_project_unknown_domain = "BadRequestProjectUnknownDomain"
    bad_request_project_unknown_classification = "BadRequestProjectUnknownClassification"
    bad_request_project_unsupported_domain_type_change = "BadRequestProjectUnsupportedDomainTypeChange"
    bad_request_project_unsupported_export_platform = "BadRequestProjectUnsupportedExportPlatform"
    bad_request_project_image_preprocessing_settings = "BadRequestProjectImagePreprocessingSettings"
    bad_request_project_duplicated = "BadRequestProjectDuplicated"
    bad_request_iteration_name = "BadRequestIterationName"
    bad_request_iteration_name_not_unique = "BadRequestIterationNameNotUnique"
    bad_request_iteration_description = "BadRequestIterationDescription"
    bad_request_iteration_is_not_trained = "BadRequestIterationIsNotTrained"
    bad_request_iteration_validation_failed = "BadRequestIterationValidationFailed"
    bad_request_workspace_cannot_be_modified = "BadRequestWorkspaceCannotBeModified"
    bad_request_workspace_not_deletable = "BadRequestWorkspaceNotDeletable"
    bad_request_tag_name = "BadRequestTagName"
    bad_request_tag_name_not_unique = "BadRequestTagNameNotUnique"
    bad_request_tag_description = "BadRequestTagDescription"
    bad_request_tag_type = "BadRequestTagType"
    bad_request_multiple_negative_tag = "BadRequestMultipleNegativeTag"
    bad_request_multiple_general_product_tag = "BadRequestMultipleGeneralProductTag"
    bad_request_image_tags = "BadRequestImageTags"
    bad_request_image_regions = "BadRequestImageRegions"
    bad_request_negative_and_regular_tag_on_same_image = "BadRequestNegativeAndRegularTagOnSameImage"
    bad_request_unsupported_domain = "BadRequestUnsupportedDomain"
    bad_request_required_param_is_null = "BadRequestRequiredParamIsNull"
    bad_request_iteration_is_published = "BadRequestIterationIsPublished"
    bad_request_invalid_publish_name = "BadRequestInvalidPublishName"
    bad_request_invalid_publish_target = "BadRequestInvalidPublishTarget"
    bad_request_unpublish_failed = "BadRequestUnpublishFailed"
    bad_request_iteration_not_published = "BadRequestIterationNotPublished"
    bad_request_subscription_api = "BadRequestSubscriptionApi"
    bad_request_exceed_project_limit = "BadRequestExceedProjectLimit"
    bad_request_exceed_iteration_per_project_limit = "BadRequestExceedIterationPerProjectLimit"
    bad_request_exceed_tag_per_project_limit = "BadRequestExceedTagPerProjectLimit"
    bad_request_exceed_tag_per_image_limit = "BadRequestExceedTagPerImageLimit"
    bad_request_exceeded_quota = "BadRequestExceededQuota"
    bad_request_cannot_migrate_project_with_name = "BadRequestCannotMigrateProjectWithName"
    bad_request_not_limited_trial = "BadRequestNotLimitedTrial"
    bad_request_image_batch = "BadRequestImageBatch"
    bad_request_image_stream = "BadRequestImageStream"
    bad_request_image_url = "BadRequestImageUrl"
    bad_request_image_format = "BadRequestImageFormat"
    bad_request_image_size_bytes = "BadRequestImageSizeBytes"
    bad_request_image_dimensions = "BadRequestImageDimensions"
    bad_request_image_aspect_ratio = "BadRequestImageAspectRatio"
    bad_request_image_exceeded_count = "BadRequestImageExceededCount"
    bad_request_training_not_needed = "BadRequestTrainingNotNeeded"
    bad_request_training_not_needed_but_training_pipeline_updated = "BadRequestTrainingNotNeededButTrainingPipelineUpdated"
    bad_request_training_validation_failed = "BadRequestTrainingValidationFailed"
    bad_request_classification_training_validation_failed = "BadRequestClassificationTrainingValidationFailed"
    bad_request_multi_class_classification_training_validation_failed = "BadRequestMultiClassClassificationTrainingValidationFailed"
    bad_request_multi_label_classification_training_validation_failed = "BadRequestMultiLabelClassificationTrainingValidationFailed"
    bad_request_detection_training_validation_failed = "BadRequestDetectionTrainingValidationFailed"
    bad_request_training_already_in_progress = "BadRequestTrainingAlreadyInProgress"
    bad_request_detection_training_not_allow_negative_tag = "BadRequestDetectionTrainingNotAllowNegativeTag"
    bad_request_invalid_email_address = "BadRequestInvalidEmailAddress"
    bad_request_retired_domain_not_supported_for_training = "BadRequestRetiredDomainNotSupportedForTraining"
    bad_request_domain_not_supported_for_advanced_training = "BadRequestDomainNotSupportedForAdvancedTraining"
    bad_request_export_platform_not_supported_for_advanced_training = "BadRequestExportPlatformNotSupportedForAdvancedTraining"
    bad_request_reserved_budget_in_hours_not_enough_for_advanced_training = "BadRequestReservedBudgetInHoursNotEnoughForAdvancedTraining"
    bad_request_custom_base_model_iteration_status_not_completed = "BadRequestCustomBaseModelIterationStatusNotCompleted"
    bad_request_custom_base_model_domain_not_compatible = "BadRequestCustomBaseModelDomainNotCompatible"
    bad_request_custom_base_model_architecture_retired = "BadRequestCustomBaseModelArchitectureRetired"
    bad_request_export_validation_failed = "BadRequestExportValidationFailed"
    bad_request_export_already_in_progress = "BadRequestExportAlreadyInProgress"
    bad_request_prediction_ids_missing = "BadRequestPredictionIdsMissing"
    bad_request_prediction_ids_exceeded_count = "BadRequestPredictionIdsExceededCount"
    bad_request_prediction_tags_exceeded_count = "BadRequestPredictionTagsExceededCount"
    bad_request_prediction_results_exceeded_count = "BadRequestPredictionResultsExceededCount"
    bad_request_prediction_invalid_application_name = "BadRequestPredictionInvalidApplicationName"
    bad_request_prediction_invalid_query_parameters = "BadRequestPredictionInvalidQueryParameters"
    bad_request_invalid_import_token = "BadRequestInvalidImportToken"
    bad_request_export_while_training = "BadRequestExportWhileTraining"
    bad_request_image_metadata_key = "BadRequestImageMetadataKey"
    bad_request_image_metadata_value = "BadRequestImageMetadataValue"
    bad_request_operation_not_supported = "BadRequestOperationNotSupported"
    bad_request_invalid_artifact_uri = "BadRequestInvalidArtifactUri"
    bad_request_customer_managed_key_revoked = "BadRequestCustomerManagedKeyRevoked"
    bad_request_invalid_uri = "BadRequestInvalidUri"
    bad_request_invalid = "BadRequestInvalid"
    unsupported_media_type = "UnsupportedMediaType"
    forbidden = "Forbidden"
    forbidden_user = "ForbiddenUser"
    forbidden_user_resource = "ForbiddenUserResource"
    forbidden_user_signup_disabled = "ForbiddenUserSignupDisabled"
    forbidden_user_signup_allowance_exceeded = "ForbiddenUserSignupAllowanceExceeded"
    forbidden_user_does_not_exist = "ForbiddenUserDoesNotExist"
    forbidden_user_disabled = "ForbiddenUserDisabled"
    forbidden_user_insufficient_capability = "ForbiddenUserInsufficientCapability"
    forbidden_dr_mode_enabled = "ForbiddenDRModeEnabled"
    forbidden_invalid = "ForbiddenInvalid"
    not_found = "NotFound"
    not_found_project = "NotFoundProject"
    not_found_project_default_iteration = "NotFoundProjectDefaultIteration"
    not_found_iteration = "NotFoundIteration"
    not_found_iteration_performance = "NotFoundIterationPerformance"
    not_found_tag = "NotFoundTag"
    not_found_image = "NotFoundImage"
    not_found_domain = "NotFoundDomain"
    not_found_apim_subscription = "NotFoundApimSubscription"
    not_found_invalid = "NotFoundInvalid"
    conflict = "Conflict"
    conflict_invalid = "ConflictInvalid"
    error_unknown = "ErrorUnknown"
    error_iteration_copy_failed = "ErrorIterationCopyFailed"
    error_prepare_performance_migration_failed = "ErrorPreparePerformanceMigrationFailed"
    error_project_invalid_workspace = "ErrorProjectInvalidWorkspace"
    error_project_invalid_pipeline_configuration = "ErrorProjectInvalidPipelineConfiguration"
    error_project_invalid_domain = "ErrorProjectInvalidDomain"
    error_project_training_request_failed = "ErrorProjectTrainingRequestFailed"
    error_project_import_request_failed = "ErrorProjectImportRequestFailed"
    error_project_export_request_failed = "ErrorProjectExportRequestFailed"
    error_featurization_service_unavailable = "ErrorFeaturizationServiceUnavailable"
    error_featurization_queue_timeout = "ErrorFeaturizationQueueTimeout"
    error_featurization_invalid_featurizer = "ErrorFeaturizationInvalidFeaturizer"
    error_featurization_augmentation_unavailable = "ErrorFeaturizationAugmentationUnavailable"
    error_featurization_unrecognized_job = "ErrorFeaturizationUnrecognizedJob"
    error_featurization_augmentation_error = "ErrorFeaturizationAugmentationError"
    error_exporter_invalid_platform = "ErrorExporterInvalidPlatform"
    error_exporter_invalid_featurizer = "ErrorExporterInvalidFeaturizer"
    error_exporter_invalid_classifier = "ErrorExporterInvalidClassifier"
    error_prediction_service_unavailable = "ErrorPredictionServiceUnavailable"
    error_prediction_model_not_found = "ErrorPredictionModelNotFound"
    error_prediction_model_not_cached = "ErrorPredictionModelNotCached"
    error_prediction = "ErrorPrediction"
    error_prediction_storage = "ErrorPredictionStorage"
    error_region_proposal = "ErrorRegionProposal"
    error_unknown_base_model = "ErrorUnknownBaseModel"
    error_server_time_out = "ErrorServerTimeOut"
    error_invalid = "ErrorInvalid"


class DomainType(str, Enum):

    classification = "Classification"
    object_detection = "ObjectDetection"


class ExportPlatform(str, Enum):

    core_ml = "CoreML"
    tensor_flow = "TensorFlow"
    docker_file = "DockerFile"
    onnx = "ONNX"
    vaidk = "VAIDK"
    open_vino = "OpenVino"


class ExportStatus(str, Enum):

    exporting = "Exporting"
    failed = "Failed"
    done = "Done"


class ExportFlavor(str, Enum):

    linux = "Linux"
    windows = "Windows"
    onnx10 = "ONNX10"
    onnx12 = "ONNX12"
    arm = "ARM"
    tensor_flow_normal = "TensorFlowNormal"
    tensor_flow_lite = "TensorFlowLite"


class ImageCreateStatus(str, Enum):

    ok = "OK"
    ok_duplicate = "OKDuplicate"
    error_source = "ErrorSource"
    error_image_format = "ErrorImageFormat"
    error_image_size = "ErrorImageSize"
    error_storage = "ErrorStorage"
    error_limit_exceed = "ErrorLimitExceed"
    error_tag_limit_exceed = "ErrorTagLimitExceed"
    error_region_limit_exceed = "ErrorRegionLimitExceed"
    error_unknown = "ErrorUnknown"
    error_negative_and_regular_tag_on_same_image = "ErrorNegativeAndRegularTagOnSameImage"
    error_image_dimensions = "ErrorImageDimensions"
    error_invalid_tag = "ErrorInvalidTag"


class ImageMetadataUpdateStatus(str, Enum):

    ok = "OK"
    error_image_not_found = "ErrorImageNotFound"
    error_limit_exceed = "ErrorLimitExceed"
    error_unknown = "ErrorUnknown"


class TagType(str, Enum):

    regular = "Regular"
    negative = "Negative"
    general_product = "GeneralProduct"


class Classifier(str, Enum):

    multiclass = "Multiclass"
    multilabel = "Multilabel"


class TrainingType(str, Enum):

    regular = "Regular"
    advanced = "Advanced"


class OrderBy(str, Enum):

    newest = "Newest"
    oldest = "Oldest"
    suggested = "Suggested"


class ProjectStatus(str, Enum):

    succeeded = "Succeeded"
    importing = "Importing"
    failed = "Failed"


class SortBy(str, Enum):

    uncertainty_ascending = "UncertaintyAscending"
    uncertainty_descending = "UncertaintyDescending"
