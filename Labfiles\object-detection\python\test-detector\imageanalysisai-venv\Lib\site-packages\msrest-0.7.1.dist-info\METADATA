Metadata-Version: 2.1
Name: msrest
Version: 0.7.1
Summary: AutoRest swagger generator Python client runtime.
Home-page: https://github.com/Azure/msrest-for-python
Author: Microsoft Corporation
License: MIT License
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: License :: OSI Approved :: MIT License
Classifier: Topic :: Software Development
Requires-Python: >=3.6
Requires-Dist: azure-core (>=1.24.0)
Requires-Dist: certifi (>=2017.4.17)
Requires-Dist: isodate (>=0.6.0)
Requires-Dist: requests-oauthlib (>=0.5.0)
Requires-Dist: requests (~=2.16)
Provides-Extra: async
Requires-Dist: aiodns ; (python_version>='3.5') and extra == 'async'
Requires-Dist: aiohttp (>=3.0) ; (python_version>='3.5') and extra == 'async'

AutoRest: Python Client Runtime
===============================

.. image:: https://travis-ci.org/Azure/msrest-for-python.svg?branch=master
 :target: https://travis-ci.org/Azure/msrest-for-python

.. image:: https://codecov.io/gh/azure/msrest-for-python/branch/master/graph/badge.svg
 :target: https://codecov.io/gh/azure/msrest-for-python

Installation
------------

To install:

.. code-block:: bash

    $ pip install msrest


Release History
---------------

2022-06-10 Version 0.7.1
+++++++++++++++++++++++++

**Bugfixes**

- Declare correctly msrest as Python 3.6 and more only for clarity  #251


2022-06-07 Version 0.7.0
+++++++++++++++++++++++++

**Features**

- Add `azure-core` as installation requirement #247
- Replace `SerializationError` and `DeserializationError` in `msrest.exceptions` with those in `azure.core` #247

**Bugfixes**

- Typing annotation in LROPoller (thanks to akx)  #242

Thanks to kianmeng for typo fixes in the documentation.

2021-01-26 Version 0.6.21
+++++++++++++++++++++++++

**Bug Fixes**

- Fixes `failsafe_deserialize` introduced in `0.6.20` #232

2021-01-25 Version 0.6.20
+++++++++++++++++++++++++

**Features**

- Add `failsafe_deserialize` method to the `Deserializer` object. #232
- Serialize `datetime`, `date`, `time`, `timedelta` and `Decimal` correctly when serializing `object` . #224

2020-09-08 Version 0.6.19
+++++++++++++++++++++++++

**Bugfixes**

- Fix serialization of random Model object  #220
- Fix serialization of unicode string in Py2 and object mode #221


2020-07-27 Version 0.6.18
+++++++++++++++++++++++++

**Features**

- Add support for attributes/text in the same XML node  #218


2020-06-25 Version 0.6.17
+++++++++++++++++++++++++

**Bugfixes**

- Fix XML and discriminator  #214


2020-06-09 Version 0.6.16
+++++++++++++++++++++++++

**Bugfixes**

- Fix XML parsing with namespaces and attributes  #209

**Features**

- Add py.typed for mypy support


2020-06-04 Version 0.6.15
+++++++++++++++++++++++++

**Bugfixes**

- Fix RFC regression introduced in 0.6.14 (RFC parse date are no longer pickable)  #208
- Fix XML parsing with namespaces  #206

Thanks to ivanst0 for the contribution


2020-05-18 Version 0.6.14
+++++++++++++++++++++++++

**Bugfixes**

- Fix "from_dict" in some complex flattening scenario  #204
- Fix RFC date parsing if machine locale is not English  #201


2020-04-07 Version 0.6.13
+++++++++++++++++++++++++

**Bugfixes**

- Fix deserializer and flattening if intermediate node is None  #198
- Fix validation exception message for minimum/maximum checks  #199


2020-04-06 Version 0.6.12
+++++++++++++++++++++++++

**Features**

- Add "time" serializer/deserializer  #196

2020-01-30 Version 0.6.11
+++++++++++++++++++++++++

**Features**

- XML mode can now be enabled even if the given Model has no XML metadata  #184
- Add Kerberos Authentication  #186
- Improve error message if expected type is dictionary and something else is provided  #188

**Bugfixes**

- Fix comma separated serialization of array in query  #186
- Fix validation of basic types in some complex scenario  #189

Thanks to catatonicprime for the contribution

2019-09-04 Version 0.6.10
+++++++++++++++++++++++++

**Features**

- XML mode now supports OpenAPI additional properties  # 174

**Bugfixes**

- Accept "is_xml" kwargs to force XML serialization  #178
- Disable XML deserialization if received element is not an ElementTree  #178
- A "null" enum deserialize as None, and not "None" anymore  #173
- Fix some UTF8 encoding issue in Python 2.7 and XML mode  #172


2019-07-24 Version 0.6.9
++++++++++++++++++++++++

**Features**

- Accept extensions of JSON mimetype as valid JSON  #167

2019-06-24 Version 0.6.8
++++++++++++++++++++++++

**BugFixes**

- Impossible to serialize XML if model contains UTF8 characters on Python 2.7  #165
- Impossible to deserialize a HTTP response as XML if body contains UTF8 characters on Python 2.7  #165
- Loading a serialized configuration fails with NameError on NoOptionError  #162

Thanks to cclauss for the contribution

2019-06-12 Version 0.6.7
++++++++++++++++++++++++

**Features**

- Add DomainCredentials credentials for EventGrid

Thanks to kalyanaj for the contribution

2019-03-21 Version 0.6.6
++++++++++++++++++++++++

**Bugfixes**

- Make 0.6.x series compatible with pyinstaller again
- sdist now includes tests

Thanks to dotlambda for the contribution

2019-03-11 Version 0.6.5
++++++++++++++++++++++++

**Bugfixes**

- Fix list of integers serialization if div is provided #151
- Fix parsing of UTF8 with BOM #145

Thanks to eduardomourar for the contribution

2019-01-09 Version 0.6.4
++++++++++++++++++++++++

**Bugfixes**

- Fix regression on credentials configuration if used outside of Autorest scope #135

2019-01-08 Version 0.6.3
++++++++++++++++++++++++

**Features**

- Updated **experimental** async support. Requires Autorest.Python 4.0.64.

2018-11-19 Version 0.6.2
++++++++++++++++++++++++

**Bugfixes**

- Fix circular dependency in TYPE_CHECKING mode #128

2018-10-15 Version 0.6.1
++++++++++++++++++++++++

**Bugfixes**

- Remove unnecessary verbose "warnings" log #126

2018-10-02 Version 0.6.0
++++++++++++++++++++++++

**Features**

- The environment variable AZURE_HTTP_USER_AGENT, if present, is now injected part of the UserAgent
- New **preview** msrest.universal_http module. Provide tools to generic HTTP management (sync/async, requests/aiohttp, etc.)
- New **preview** msrest.pipeline implementation:

  - A Pipeline is an ordered list of Policies than can process an HTTP request and response in a generic way.
  - More details in the wiki page about Pipeline: https://github.com/Azure/msrest-for-python/wiki/msrest-0.6.0---Pipeline

- Adding new attributes to Configuration instance:

  - http_logger_policy - Policy to handle HTTP logging
  - user_agent_policy - Policy to handle UserAgent
  - pipeline - The current pipeline used by the SDK client
  - async_pipeline - The current async pipeline used by the async SDK client

- Installing "msrest[async]" now installs the **experimental** async support. Works ONLY for Autorest.Python 4.0.63.

**Breaking changes**

- The HTTPDriver API introduced in 0.5.0 has been replaced by the Pipeline implementation.

- The following classes have been moved from "msrest.pipeline" to "msrest.universal_http":

  - ClientRedirectPolicy
  - ClientProxies
  - ClientConnection

- The following classes have been moved from "msrest.pipeline" to "msrest.universal_http.requests":

  - ClientRetryPolicy

**Bugfixes**

- Fix "long" on Python 2 if used with the "object" type  #121

Thanks to robgolding for the contribution

2018-09-04 Version 0.5.5
++++++++++++++++++++++++

**Bugfixes**

- Fix a serialization issue if additional_properties is declared, and "automatic model" syntax is used
  ("automatic model" being the ability to pass a dict to command and have the model auto-created)  # 120

2018-07-12 Version 0.5.4
++++++++++++++++++++++++

**Features**

- Support additionalProperties and XML

**BugFixes**

- Better parse empty node and not string types
- Improve "object" XML parsing

2018-07-10 Version 0.5.3
++++++++++++++++++++++++

**BugFixes**

- Fix some XML serialization subtle scenarios

2018-07-09 Version 0.5.2
++++++++++++++++++++++++

**Features**

- deserialize/from_dict now accepts a content-type parameter to parse XML strings

**Bugfixes**

- Fix some complex XML Swagger definitions.

This release likely breaks already generated XML SDKs, that needs to be regenerated with autorest.python 3.0.58

2018-06-21 Version 0.5.1
++++++++++++++++++++++++

**Bugfixes**

- Lower Accept header overwrite logging message #110
- Fix 'object' type and XML format

Thanks to dharmab for the contribution

2018-06-12 Version 0.5.0
++++++++++++++++++++++++

**Disclaimer**

This released is designed to be backward compatible with 0.4.x, but there is too many internal refactoring
and new features to continue with 0.4.x versioning

**Features**

- Add XML support
- Add many type hints, and MyPY testing on CI.
- HTTP calls are made through a HTTPDriver API. Only implementation is `requests` for now. This driver API is *not* considered stable
  and you should pin your msrest version if you want to provide a personal implementation.

**Bugfixes**

- Incorrect milliseconds serialization for some datetime object #94

**Deprecation**

That will trigger a DeprecationWarning if an old Autorest generated code is used.

- _client.add_header is deprecated, and config.headers should be used instead
- _client.send_formdata is deprecated, and _client.put/get/delete/post + _client.send should be used instead

2018-04-30 Version 0.4.29
+++++++++++++++++++++++++

**Bugfixes**

- Improve `SDKClient.__exit__` to take exc_details as optional parameters and not required #93
- refresh_session should also use the permanent HTTP session if available #91

2018-04-18 Version 0.4.28
+++++++++++++++++++++++++

**Features**

- msrest is now able to keep the "requests.Session" alive for performance. To activate this behavior:

  - Use the final Client as a context manager (requires generation with Autorest.Python 3.0.50 at least)
  - Use `client.config.keep_alive = True` and `client.close()` (requires generation with Autorest.Python 3.0.50 at least)
  - Use `client.config.keep_alive = True` and client._client.close() (not recommended, but available in old releases of SDK)

- All Authentication classes now define `signed_session` and `refresh_session` with an optional `session` parameter.
  To take benefits of the session improvement, a subclass of Authentication *MUST* add this optional parameter
  and use it if it's not `None`:

     def signed_session(self, session=None):
         session = session or requests.Session()

         # As usual from here.

2018-03-07 Version 0.4.27
+++++++++++++++++++++++++

**Features**

- Disable HTTP log by default (security), add `enable_http_log` to restore it #86

**BugFixes**

- Fix incorrect date parsing if ms precision is over 6 digits #82

2018-01-30 Version 0.4.26
+++++++++++++++++++++++++

**Features**

- Add TopicCredentials for EventGrid client

**Bugfixes**

- Fix minimal dependency of isodate
- Fix serialisation from dict if datetime provided

2018-01-08 Version 0.4.25
+++++++++++++++++++++++++

**Features**

- Add LROPoller class. This is a customizable LRO engine.
  This is the poller engine of Autorest.Python 3.0, and is not used by code generated by previous Autorest version.

2018-01-03 Version 0.4.24
+++++++++++++++++++++++++

**Bugfixes**

- Date parsing is now compliant with Autorest / Swagger 2.0 specification (less lenient)

**Internal optimisation**

- Call that does not return a streamable object are now executed in requests stream mode False (was True whatever the type of the call).
  This should reduce the number of leaked opened session and allow urllib3 to manage connection pooling more efficiently.
  Only clients generated with Autorest.Python >= 2.1.31 (not impacted otherwise, fully backward compatible)

2017-12-21 Version 0.4.23
+++++++++++++++++++++++++

**Bugfixes**

- Accept to deserialize enum of different type if content string match #75
- Stop failing on deserialization if enum string is unkwon. Return the string instead.

**Features**

- Model now accept kwargs in constructor for future kwargs models

2017-12-15 Version 0.4.22
+++++++++++++++++++++++++

**Bugfixes**

- Do not validate additional_properties #73
- Improve validation error if expected type is dict, but actual type is not #73

2017-12-14 Version 0.4.21
+++++++++++++++++++++++++

**Bugfixes**

- Fix additional_properties if Swagger was flatten #72

2017-12-13 Version 0.4.20
+++++++++++++++++++++++++

**Features**

- Add support for additional_properties

  - By default, all additional_properties are kept.
  - Additional properties are sent to the server only if it was specified in the Swagger,
    or if "enable_additional_properties_sending" is called on the model we want it.
    This is a class method that enables it for all instance of this model.

2017-11-20 Version 0.4.19
+++++++++++++++++++++++++

**Features**

- The interpretation of Swagger 2.0 "discriminator" is now lenient. This means for these two scenarios:

  - Discriminator value is missing from the received payload
  - Discriminator value is not defined in the Swagger

  Instead of failing with an exception, this now returns the base type for this "discriminator".

  Note that this is not a contradiction of the Swagger 2.0 spec, that specifies
  "validation SHOULD fail [...] there may exist valid reasons in particular circumstances to ignore a particular item,
  but the full implications must be understood and carefully weighed before choosing a different course."

  This cannot be configured for now and is the new default behvaior, but can be in the future if needed.

**Bugfixes**

- Optional formdata parameters were raising an exception (#65)
- "application/x-www-form-urlencoded" form was sent using "multipart/form-data".
  This causes problems if the server does not support "multipart/form-data" (#66)

2017-10-26 Version 0.4.18
+++++++++++++++++++++++++

**Features**

- Add ApiKeyCredentials class. This can be used to support OpenAPI ApiKey feature.
- Add CognitiveServicesAuthentication class. Pre-declared ApiKeyCredentials class for Cognitive Services.

2017-10-12 Version 0.4.17
+++++++++++++++++++++++++

**Features**

This make Authentication classes more consistent:

- OAuthTokenAuthentication is now a subclass of BasicTokenAuthentication (was Authentication)
- BasicTokenAuthentication has now a "set_token" methods that does nothing.

This allows test like "isintance(o, BasicTokenAuthentication)" to be guaranteed that the following attributes exists:

- token
- set_token()
- signed_session()

This means for users of "msrestazure", that they are guaranteed that all AD classes somehow inherits from "BasicTokenAuthentication"

2017-10-05 Version 0.4.16
+++++++++++++++++++++++++

**Bugfixes**

- Fix regression: accept "set<str>" as a valid "[str]" (#60)

2017-09-28 Version 0.4.15
+++++++++++++++++++++++++

**Bugfixes**

- Always log response body (#16)
- Improved exception message if error JSON is Odata v4 (#55)
- Refuse "str" as a valid "[str]" type (#41)
- Better exception handling if input from server is not JSON valid

**Features**

- Add Configuration.session_configuration_callback to customize the requests.Session if necessary (#52)
- Add a flag to Serializer to disable client-side-validation (#51)
- Remove "import requests" from "exceptions.py" for apps that require fast loading time (#23)

Thank you to jayden-at-arista for the contribution

2017-08-23 Version 0.4.14
+++++++++++++++++++++++++

**Bugfixes**

- Fix regression introduced in msrest 0.4.12 - dict syntax with enum modeled as string and enum used

2017-08-22 Version 0.4.13
+++++++++++++++++++++++++

**Bugfixes**

- Fix regression introduced in msrest 0.4.12 - dict syntax using isodate.Duration (#42)

2017-08-21 Version 0.4.12
+++++++++++++++++++++++++

**Features**

- Input is now more lenient
- Model have a "validate" method to check content constraints
- Model have now 4 new methods:

  - "serialize" that gives the RestAPI that will be sent
  - "as_dict" that returns a dict version of the Model. Callbacks are available.
  - "deserialize" the parses the RestAPI JSON into a Model
  - "from_dict" that parses several dict syntax into a Model. Callbacks are available.

More details and examples in the Wiki article on Github:
https://github.com/Azure/msrest-for-python/wiki/msrest-0.4.12---Serialization-change

**Bugfixes**

- Better Enum checking (#38)

2017-06-21 Version 0.4.11
+++++++++++++++++++++++++

**Bugfixes**

- Fix incorrect dependency to "requests" 2.14.x, instead of 2.x meant in 0.4.8

2017-06-15 Version 0.4.10
+++++++++++++++++++++++++

**Features**

- Add requests hooks to configuration

2017-06-08 Version 0.4.9
++++++++++++++++++++++++

**Bugfixes**

- Accept "null" value for paging array as an empty list and do not raise (#30)

2017-05-22 Version 0.4.8
++++++++++++++++++++++++

**Bugfixes**

- Fix random "pool is closed" error (#29)
- Fix requests dependency to version 2.x, since version 3.x is annunced to be breaking.

2017-04-04 Version 0.4.7
++++++++++++++++++++++++

**BugFixes**

- Refactor paging #22:

   - "next" is renamed "advance_page" and "next" returns only 1 element (Python 2 expected behavior)
   - paging objects are now real generator and support the "next()" built-in function without need for "iter()"

- Raise accurate DeserialisationError on incorrect RestAPI discriminator usage #27
- Fix discriminator usage of the base class name #27
- Remove default mutable arguments in Clients #20
- Fix object comparison in some scenarios #24

2017-03-06 Version 0.4.6
++++++++++++++++++++++++

**Bugfixes**

- Allow Model sub-classes to be serialized if type is "object"

2017-02-13 Version 0.4.5
++++++++++++++++++++++++

**Bugfixes**

- Fix polymorphic deserialization #11
- Fix regexp validation if '\\w' is used in Python 2.7 #13
- Fix dict deserialization if keys are unicode in Python 2.7

**Improvements**

- Add polymorphic serialisation from dict objects
- Remove chardet and use HTTP charset declaration (fallback to utf8)

2016-09-14 Version 0.4.4
++++++++++++++++++++++++

**Bugfixes**

- Remove paging URL validation, part of fix https://github.com/Azure/autorest/pull/1420

**Disclaimer**

In order to get paging fixes for impacted clients, you need this package and Autorest > 0.17.0 Nightly 20160913

2016-09-01 Version 0.4.3
++++++++++++++++++++++++

**Bugfixes**

- Better exception message (https://github.com/Azure/autorest/pull/1300)

2016-08-15 Version 0.4.2
++++++++++++++++++++++++

**Bugfixes**

- Fix serialization if "object" type contains None (https://github.com/Azure/autorest/issues/1353)

2016-08-08 Version 0.4.1
++++++++++++++++++++++++

**Bugfixes**

- Fix compatibility issues with requests 2.11.0 (https://github.com/Azure/autorest/issues/1337)
- Allow url of ClientRequest to have parameters (https://github.com/Azure/autorest/issues/1217)

2016-05-25 Version 0.4.0
++++++++++++++++++++++++

This version has no bug fixes, but implements new features of Autorest:
- Base64 url type
- unixtime type
- x-ms-enum modelAsString flag

**Behaviour changes**

- Add Platform information in UserAgent
- Needs Autorest > 0.17.0 Nightly 20160525

2016-04-26 Version 0.3.0
++++++++++++++++++++++++

**Bugfixes**

- Read only values are no longer in __init__ or sent to the server (https://github.com/Azure/autorest/pull/959)
- Useless kwarg removed

**Behaviour changes**

- Needs Autorest > 0.16.0 Nightly 20160426


2016-03-25 Version 0.2.0
++++++++++++++++++++++++

**Bugfixes**

- Manage integer enum values (https://github.com/Azure/autorest/pull/879)
- Add missing application/json Accept HTTP header (https://github.com/Azure/azure-sdk-for-python/issues/553)

**Behaviour changes**

- Needs Autorest > 0.16.0 Nightly 20160324


2016-03-21 Version 0.1.3
++++++++++++++++++++++++

**Bugfixes**

- Deserialisation of generic resource if null in JSON (https://github.com/Azure/azure-sdk-for-python/issues/544)


2016-03-14 Version 0.1.2
++++++++++++++++++++++++

**Bugfixes**

- urllib3 side effect (https://github.com/Azure/autorest/issues/824)


2016-03-04 Version 0.1.1
++++++++++++++++++++++++

**Bugfixes**

- Source package corrupted in Pypi (https://github.com/Azure/autorest/issues/799)

2016-03-04 Version 0.1.0
+++++++++++++++++++++++++

**Behavioural Changes**

- Removed custom logging set up and configuration. All loggers are now children of the root logger 'msrest' with no pre-defined configurations.
- Replaced _required attribute in Model class with more extensive _validation dict.

**Improvement**

- Removed hierarchy scanning for attribute maps from base Model class - relies on generator to populate attribute
  maps according to hierarchy.
- Base class Paged now inherits from collections.Iterable.
- Data validation during serialization using custom parameters (e.g. max, min etc).
- Added ValidationError to be raised if invalid data encountered during serialization.

2016-02-29 Version 0.0.3
++++++++++++++++++++++++

**Bugfixes**

- Source package corrupted in Pypi (https://github.com/Azure/autorest/issues/718)

2016-02-19 Version 0.0.2
++++++++++++++++++++++++

**Bugfixes**

- Fixed bug in exception logging before logger configured.

2016-02-19 Version 0.0.1
++++++++++++++++++++++++

- Initial release.


