from dotenv import load_dotenv
import os
import sys
from PIL import Image, ImageDraw
from matplotlib import pyplot as plt

# Import namespaces
from azure.ai.vision.face import FaceClient
from azure.ai.vision.face.models import FaceDetectionModel, FaceRecognitionModel, FaceAttributeTypeDetection01
from azure.core.credentials import AzureKeyCredential


def main():

    # Clear the console
    # os.system('cls' if os.name=='nt' else 'clear')  # Commented out to see output
    print("Starting face analysis...")

    try:
        # Get Configuration Settings
        load_dotenv()
        cog_endpoint = os.getenv('AI_SERVICE_ENDPOINT')
        cog_key = os.getenv('AI_SERVICE_KEY')

        # Get image
        image_file = 'images/face1.jpg'
        if len(sys.argv) > 1:
            image_file = sys.argv[1]


        # Authenticate Face client
        face_client = FaceClient(
            endpoint=cog_endpoint,
            credential=AzureKeyCredential(cog_key))



        # Specify facial features to be retrieved
        features = [FaceAttributeTypeDetection01.ACCESSORIES,
                   FaceAttributeTypeDetection01.BLUR,
                   FaceAttributeTypeDetection01.EXPOSURE,
                   FaceAttributeTypeDetection01.GLASSES,
                   FaceAttributeTypeDetection01.HEAD_POSE,
                   FaceAttributeTypeDetection01.NOISE,
                   FaceAttributeTypeDetection01.OCCLUSION]


        # Get faces
        print(f'Analyzing faces in {image_file}...')

        with open(image_file, "rb") as f:
            image_data = f.read()

        # Simple face detection without restricted features
        detected_faces = face_client.detect(
            image_data,
            detection_model=FaceDetectionModel.DETECTION03,
            recognition_model=FaceRecognitionModel.RECOGNITION04,
            return_face_id=False,  # Don't return face ID (requires approval)
            return_face_attributes=None  # Don't use face attributes (some require approval)
        )

        if len(detected_faces) > 0:
            print(f'Found {len(detected_faces)} faces')

            # Analyze each face
            for i, face in enumerate(detected_faces):
                print(f'\nFace {i+1}:')

                # Get face rectangle (basic detection info)
                if face.face_rectangle:
                    rect = face.face_rectangle
                    print(f'  Location: x={rect.left}, y={rect.top}')
                    print(f'  Size: width={rect.width}, height={rect.height}')
                    print(f'  Area: {rect.width * rect.height} pixels')

                print(f'  Face detected successfully!')

            # Annotate faces in the image
            annotate_faces(image_file, detected_faces)
        else:
            print('No faces detected.')

    except Exception as ex:
        print(ex)

def annotate_faces(image_file, detected_faces):
    print('\nAnnotating faces in image...')

    # Prepare image for drawing
    fig = plt.figure(figsize=(8, 6))
    plt.axis('off')
    image = Image.open(image_file)
    draw = ImageDraw.Draw(image)
    color = 'lightgreen'

    # Annotate each face in the image
    face_count = 0
    for face in detected_faces:
        face_count += 1
        r = face.face_rectangle
        bounding_box = ((r.left, r.top), (r.left + r.width, r.top + r.height))
        draw = ImageDraw.Draw(image)
        draw.rectangle(bounding_box, outline=color, width=5)
        annotation = 'Face number {}'.format(face_count)
        plt.annotate(annotation,(r.left, r.top), backgroundcolor=color)
    
    # Save annotated image
    plt.imshow(image)
    outputfile = 'detected_faces.jpg'
    fig.savefig(outputfile)
    print(f'  Results saved in {outputfile}\n')


if __name__ == "__main__":
    main()