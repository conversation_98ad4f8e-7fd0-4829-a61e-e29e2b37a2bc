from dotenv import load_dotenv
import os
import sys
from PIL import Image, ImageDraw
from matplotlib import pyplot as plt

# Import namespaces
from azure.ai.vision.face import FaceClient
from azure.ai.vision.face.models import FaceDetectionModel, FaceRecognitionModel, FaceAttributeTypeDetection01
from azure.core.credentials import AzureKeyCredential


def main():

    # Clear the console
    # os.system('cls' if os.name=='nt' else 'clear')  # Commented out to see output
    print("Starting face analysis...")

    try:
        # Get Configuration Settings
        load_dotenv()
        cog_endpoint = os.getenv('AI_SERVICE_ENDPOINT')
        cog_key = os.getenv('AI_SERVICE_KEY')

        # Get image
        image_file = 'images/face1.jpg'
        if len(sys.argv) > 1:
            image_file = sys.argv[1]


        # Authenticate Face client
        face_client = FaceClient(
            endpoint=cog_endpoint,
            credential=AzureKeyCredential(cog_key))



        # Specify facial features to be retrieved
        features = [FaceAttributeTypeDetection01.AGE,
                   FaceAttributeTypeDetection01.EMOTION,
                   FaceAttributeTypeDetection01.GLASSES,
                   FaceAttributeTypeDetection01.SMILE]


        # Get faces
        print(f'Analyzing faces in {image_file}...')

        with open(image_file, "rb") as f:
            image_data = f.read()

        detected_faces = face_client.detect(
            image_data,
            detection_model=FaceDetectionModel.DETECTION03,
            recognition_model=FaceRecognitionModel.RECOGNITION04,
            return_face_id=True,
            return_face_attributes=features
        )

        if len(detected_faces) > 0:
            print(f'Found {len(detected_faces)} faces')

            # Analyze each face
            for i, face in enumerate(detected_faces):
                print(f'\nFace {i+1}:')

                # Get face attributes
                if face.face_attributes:
                    if face.face_attributes.age:
                        print(f'  Age: {face.face_attributes.age}')

                    if face.face_attributes.emotion:
                        emotions = face.face_attributes.emotion
                        print(f'  Emotions:')
                        print(f'    Anger: {emotions.anger:.2f}')
                        print(f'    Contempt: {emotions.contempt:.2f}')
                        print(f'    Disgust: {emotions.disgust:.2f}')
                        print(f'    Fear: {emotions.fear:.2f}')
                        print(f'    Happiness: {emotions.happiness:.2f}')
                        print(f'    Neutral: {emotions.neutral:.2f}')
                        print(f'    Sadness: {emotions.sadness:.2f}')
                        print(f'    Surprise: {emotions.surprise:.2f}')

                    if face.face_attributes.glasses:
                        print(f'  Glasses: {face.face_attributes.glasses}')

                    if face.face_attributes.smile:
                        print(f'  Smile: {face.face_attributes.smile:.2f}')

            # Annotate faces in the image
            annotate_faces(image_file, detected_faces)
        else:
            print('No faces detected.')

    except Exception as ex:
        print(ex)

def annotate_faces(image_file, detected_faces):
    print('\nAnnotating faces in image...')

    # Prepare image for drawing
    fig = plt.figure(figsize=(8, 6))
    plt.axis('off')
    image = Image.open(image_file)
    draw = ImageDraw.Draw(image)
    color = 'lightgreen'

    # Annotate each face in the image
    face_count = 0
    for face in detected_faces:
        face_count += 1
        r = face.face_rectangle
        bounding_box = ((r.left, r.top), (r.left + r.width, r.top + r.height))
        draw = ImageDraw.Draw(image)
        draw.rectangle(bounding_box, outline=color, width=5)
        annotation = 'Face number {}'.format(face_count)
        plt.annotate(annotation,(r.left, r.top), backgroundcolor=color)
    
    # Save annotated image
    plt.imshow(image)
    outputfile = 'detected_faces.jpg'
    fig.savefig(outputfile)
    print(f'  Results saved in {outputfile}\n')


if __name__ == "__main__":
    main()