# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) Python Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._models import CaptionResult
from ._models import CropRegion
from ._models import DenseCaption
from ._models import DenseCaptionsResult
from ._models import DetectedObject
from ._models import DetectedPerson
from ._models import DetectedTag
from ._models import DetectedTextBlock
from ._models import DetectedTextLine
from ._models import DetectedTextWord
from ._models import ImageAnalysisResult
from ._models import ImageBoundingBox
from ._models import ImageMetadata
from ._models import ImagePoint
from ._models import ObjectsResult
from ._models import PeopleResult
from ._models import ReadResult
from ._models import SmartCropsResult
from ._models import TagsResult

from ._enums import VisualFeatures
from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "CaptionResult",
    "CropRegion",
    "DenseCaption",
    "DenseCaptionsResult",
    "DetectedObject",
    "DetectedPerson",
    "DetectedTag",
    "DetectedTextBlock",
    "DetectedTextLine",
    "DetectedTextWord",
    "ImageAnalysisResult",
    "ImageBoundingBox",
    "ImageMetadata",
    "ImagePoint",
    "ObjectsResult",
    "PeopleResult",
    "ReadResult",
    "SmartCropsResult",
    "TagsResult",
    "VisualFeatures",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
