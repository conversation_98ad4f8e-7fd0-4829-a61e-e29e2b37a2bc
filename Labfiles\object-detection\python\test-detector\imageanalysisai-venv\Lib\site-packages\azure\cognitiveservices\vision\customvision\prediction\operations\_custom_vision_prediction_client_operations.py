# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
# --------------------------------------------------------------------------

from msrest.pipeline import ClientRawResponse
from .. import models


class CustomVisionPredictionClientOperationsMixin(object):

    def classify_image(
            self, project_id, published_name, image_data, application=None, custom_headers=None, raw=False, **operation_config):
        """Classify an image and saves the result.

        :param project_id: The project id.
        :type project_id: str
        :param published_name: Specifies the name of the model to evaluate
         against.
        :type published_name: str
        :param image_data: Binary image data. Supported formats are JPEG, GIF,
         PNG, and BMP. Supports images up to 4MB.
        :type image_data: Generator
        :param application: Optional. Specifies the name of application using
         the endpoint.
        :type application: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.prediction.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.prediction.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.classify_image.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'publishedName': self._serialize.url("published_name", published_name, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if application is not None:
            query_parameters['application'] = self._serialize.query("application", application, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'multipart/form-data'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct form data
        form_data_content = {
            'imageData': image_data,
        }

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, form_content=form_data_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    classify_image.metadata = {'url': '/{projectId}/classify/iterations/{publishedName}/image'}

    def classify_image_with_no_store(
            self, project_id, published_name, image_data, application=None, custom_headers=None, raw=False, **operation_config):
        """Classify an image without saving the result.

        :param project_id: The project id.
        :type project_id: str
        :param published_name: Specifies the name of the model to evaluate
         against.
        :type published_name: str
        :param image_data: Binary image data. Supported formats are JPEG, GIF,
         PNG, and BMP. Supports images up to 4MB.
        :type image_data: Generator
        :param application: Optional. Specifies the name of application using
         the endpoint.
        :type application: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.prediction.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.prediction.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.classify_image_with_no_store.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'publishedName': self._serialize.url("published_name", published_name, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if application is not None:
            query_parameters['application'] = self._serialize.query("application", application, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'multipart/form-data'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct form data
        form_data_content = {
            'imageData': image_data,
        }

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, form_content=form_data_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    classify_image_with_no_store.metadata = {'url': '/{projectId}/classify/iterations/{publishedName}/image/nostore'}

    def classify_image_url(
            self, project_id, published_name, url, application=None, custom_headers=None, raw=False, **operation_config):
        """Classify an image url and saves the result.

        :param project_id: The project id.
        :type project_id: str
        :param published_name: Specifies the name of the model to evaluate
         against.
        :type published_name: str
        :param url: Url of the image.
        :type url: str
        :param application: Optional. Specifies the name of application using
         the endpoint.
        :type application: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.prediction.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.prediction.models.CustomVisionErrorException>`
        """
        image_url = models.ImageUrl(url=url)

        # Construct URL
        url = self.classify_image_url.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'publishedName': self._serialize.url("published_name", published_name, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if application is not None:
            query_parameters['application'] = self._serialize.query("application", application, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(image_url, 'ImageUrl')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    classify_image_url.metadata = {'url': '/{projectId}/classify/iterations/{publishedName}/url'}

    def classify_image_url_with_no_store(
            self, project_id, published_name, url, application=None, custom_headers=None, raw=False, **operation_config):
        """Classify an image url without saving the result.

        :param project_id: The project id.
        :type project_id: str
        :param published_name: Specifies the name of the model to evaluate
         against.
        :type published_name: str
        :param url: Url of the image.
        :type url: str
        :param application: Optional. Specifies the name of application using
         the endpoint.
        :type application: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.prediction.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.prediction.models.CustomVisionErrorException>`
        """
        image_url = models.ImageUrl(url=url)

        # Construct URL
        url = self.classify_image_url_with_no_store.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'publishedName': self._serialize.url("published_name", published_name, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if application is not None:
            query_parameters['application'] = self._serialize.query("application", application, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(image_url, 'ImageUrl')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    classify_image_url_with_no_store.metadata = {'url': '/{projectId}/classify/iterations/{publishedName}/url/nostore'}

    def detect_image(
            self, project_id, published_name, image_data, application=None, custom_headers=None, raw=False, **operation_config):
        """Detect objects in an image and saves the result.

        :param project_id: The project id.
        :type project_id: str
        :param published_name: Specifies the name of the model to evaluate
         against.
        :type published_name: str
        :param image_data: Binary image data. Supported formats are JPEG, GIF,
         PNG, and BMP. Supports images up to 4MB.
        :type image_data: Generator
        :param application: Optional. Specifies the name of application using
         the endpoint.
        :type application: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.prediction.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.prediction.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.detect_image.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'publishedName': self._serialize.url("published_name", published_name, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if application is not None:
            query_parameters['application'] = self._serialize.query("application", application, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'multipart/form-data'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct form data
        form_data_content = {
            'imageData': image_data,
        }

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, form_content=form_data_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    detect_image.metadata = {'url': '/{projectId}/detect/iterations/{publishedName}/image'}

    def detect_image_with_no_store(
            self, project_id, published_name, image_data, application=None, custom_headers=None, raw=False, **operation_config):
        """Detect objects in an image without saving the result.

        :param project_id: The project id.
        :type project_id: str
        :param published_name: Specifies the name of the model to evaluate
         against.
        :type published_name: str
        :param image_data: Binary image data. Supported formats are JPEG, GIF,
         PNG, and BMP. Supports images up to 4MB.
        :type image_data: Generator
        :param application: Optional. Specifies the name of application using
         the endpoint.
        :type application: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.prediction.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.prediction.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.detect_image_with_no_store.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'publishedName': self._serialize.url("published_name", published_name, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if application is not None:
            query_parameters['application'] = self._serialize.query("application", application, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'multipart/form-data'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct form data
        form_data_content = {
            'imageData': image_data,
        }

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, form_content=form_data_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    detect_image_with_no_store.metadata = {'url': '/{projectId}/detect/iterations/{publishedName}/image/nostore'}

    def detect_image_url(
            self, project_id, published_name, url, application=None, custom_headers=None, raw=False, **operation_config):
        """Detect objects in an image url and saves the result.

        :param project_id: The project id.
        :type project_id: str
        :param published_name: Specifies the name of the model to evaluate
         against.
        :type published_name: str
        :param url: Url of the image.
        :type url: str
        :param application: Optional. Specifies the name of application using
         the endpoint.
        :type application: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.prediction.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.prediction.models.CustomVisionErrorException>`
        """
        image_url = models.ImageUrl(url=url)

        # Construct URL
        url = self.detect_image_url.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'publishedName': self._serialize.url("published_name", published_name, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if application is not None:
            query_parameters['application'] = self._serialize.query("application", application, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(image_url, 'ImageUrl')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    detect_image_url.metadata = {'url': '/{projectId}/detect/iterations/{publishedName}/url'}

    def detect_image_url_with_no_store(
            self, project_id, published_name, url, application=None, custom_headers=None, raw=False, **operation_config):
        """Detect objects in an image url without saving the result.

        :param project_id: The project id.
        :type project_id: str
        :param published_name: Specifies the name of the model to evaluate
         against.
        :type published_name: str
        :param url: Url of the image.
        :type url: str
        :param application: Optional. Specifies the name of application using
         the endpoint.
        :type application: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.prediction.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.prediction.models.CustomVisionErrorException>`
        """
        image_url = models.ImageUrl(url=url)

        # Construct URL
        url = self.detect_image_url_with_no_store.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'publishedName': self._serialize.url("published_name", published_name, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if application is not None:
            query_parameters['application'] = self._serialize.query("application", application, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(image_url, 'ImageUrl')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    detect_image_url_with_no_store.metadata = {'url': '/{projectId}/detect/iterations/{publishedName}/url/nostore'}
