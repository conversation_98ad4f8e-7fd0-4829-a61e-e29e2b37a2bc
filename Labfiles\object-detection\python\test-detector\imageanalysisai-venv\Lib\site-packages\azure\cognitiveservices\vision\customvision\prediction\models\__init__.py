# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
# --------------------------------------------------------------------------

try:
    from ._models_py3 import BoundingBox
    from ._models_py3 import CustomVisionError, CustomVisionErrorException
    from ._models_py3 import ImagePrediction
    from ._models_py3 import ImageUrl
    from ._models_py3 import Prediction
except (SyntaxError, ImportError):
    from ._models import BoundingBox
    from ._models import CustomVisionError, CustomVisionErrorException
    from ._models import ImagePrediction
    from ._models import ImageUrl
    from ._models import Prediction
from ._custom_vision_prediction_client_enums import (
    CustomVisionErrorCodes,
    TagType,
)

__all__ = [
    'BoundingBox',
    'CustomVisionError', 'CustomVisionErrorException',
    'ImagePrediction',
    'ImageUrl',
    'Prediction',
    'CustomVisionErrorCodes',
    'TagType',
]
