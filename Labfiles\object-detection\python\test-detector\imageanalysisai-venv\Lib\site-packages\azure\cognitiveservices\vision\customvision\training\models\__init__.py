# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
# --------------------------------------------------------------------------

try:
    from ._models_py3 import BoundingBox
    from ._models_py3 import CreateProjectOptions
    from ._models_py3 import CustomBaseModelInfo
    from ._models_py3 import CustomVisionError, CustomVisionErrorException
    from ._models_py3 import Domain
    from ._models_py3 import Export
    from ._models_py3 import Image
    from ._models_py3 import Image<PERSON>reateResult
    from ._models_py3 import ImageCreateSummary
    from ._models_py3 import ImageFileCreateBatch
    from ._models_py3 import ImageFileCreateEntry
    from ._models_py3 import ImageIdCreateBatch
    from ._models_py3 import ImageIdCreateEntry
    from ._models_py3 import ImageMetadataUpdateEntry
    from ._models_py3 import ImageMetadataUpdateSummary
    from ._models_py3 import ImagePerformance
    from ._models_py3 import ImagePrediction
    from ._models_py3 import ImageProcessingSettings
    from ._models_py3 import ImageRegion
    from ._models_py3 import ImageRegionCreateBatch
    from ._models_py3 import ImageRegionCreateEntry
    from ._models_py3 import ImageRegionCreateResult
    from ._models_py3 import ImageRegionCreateSummary
    from ._models_py3 import ImageRegionProposal
    from ._models_py3 import ImageTag
    from ._models_py3 import ImageTagCreateBatch
    from ._models_py3 import ImageTagCreateEntry
    from ._models_py3 import ImageTagCreateSummary
    from ._models_py3 import ImageUrl
    from ._models_py3 import ImageUrlCreateBatch
    from ._models_py3 import ImageUrlCreateEntry
    from ._models_py3 import Iteration
    from ._models_py3 import IterationPerformance
    from ._models_py3 import ModelInformation
    from ._models_py3 import Prediction
    from ._models_py3 import PredictionQueryResult
    from ._models_py3 import PredictionQueryTag
    from ._models_py3 import PredictionQueryToken
    from ._models_py3 import Project
    from ._models_py3 import ProjectExport
    from ._models_py3 import ProjectSettings
    from ._models_py3 import Region
    from ._models_py3 import RegionProposal
    from ._models_py3 import StoredImagePrediction
    from ._models_py3 import StoredSuggestedTagAndRegion
    from ._models_py3 import SuggestedTagAndRegion
    from ._models_py3 import SuggestedTagAndRegionQuery
    from ._models_py3 import SuggestedTagAndRegionQueryToken
    from ._models_py3 import Tag
    from ._models_py3 import TagFilter
    from ._models_py3 import TagPerformance
    from ._models_py3 import TrainingParameters
except (SyntaxError, ImportError):
    from ._models import BoundingBox
    from ._models import CreateProjectOptions
    from ._models import CustomBaseModelInfo
    from ._models import CustomVisionError, CustomVisionErrorException
    from ._models import Domain
    from ._models import Export
    from ._models import Image
    from ._models import ImageCreateResult
    from ._models import ImageCreateSummary
    from ._models import ImageFileCreateBatch
    from ._models import ImageFileCreateEntry
    from ._models import ImageIdCreateBatch
    from ._models import ImageIdCreateEntry
    from ._models import ImageMetadataUpdateEntry
    from ._models import ImageMetadataUpdateSummary
    from ._models import ImagePerformance
    from ._models import ImagePrediction
    from ._models import ImageProcessingSettings
    from ._models import ImageRegion
    from ._models import ImageRegionCreateBatch
    from ._models import ImageRegionCreateEntry
    from ._models import ImageRegionCreateResult
    from ._models import ImageRegionCreateSummary
    from ._models import ImageRegionProposal
    from ._models import ImageTag
    from ._models import ImageTagCreateBatch
    from ._models import ImageTagCreateEntry
    from ._models import ImageTagCreateSummary
    from ._models import ImageUrl
    from ._models import ImageUrlCreateBatch
    from ._models import ImageUrlCreateEntry
    from ._models import Iteration
    from ._models import IterationPerformance
    from ._models import ModelInformation
    from ._models import Prediction
    from ._models import PredictionQueryResult
    from ._models import PredictionQueryTag
    from ._models import PredictionQueryToken
    from ._models import Project
    from ._models import ProjectExport
    from ._models import ProjectSettings
    from ._models import Region
    from ._models import RegionProposal
    from ._models import StoredImagePrediction
    from ._models import StoredSuggestedTagAndRegion
    from ._models import SuggestedTagAndRegion
    from ._models import SuggestedTagAndRegionQuery
    from ._models import SuggestedTagAndRegionQueryToken
    from ._models import Tag
    from ._models import TagFilter
    from ._models import TagPerformance
    from ._models import TrainingParameters
from ._custom_vision_training_client_enums import (
    Classifier,
    CustomVisionErrorCodes,
    DomainType,
    ExportFlavor,
    ExportPlatform,
    ExportStatus,
    ImageCreateStatus,
    ImageMetadataUpdateStatus,
    OrderBy,
    ProjectStatus,
    SortBy,
    TagType,
    TrainingType,
)

__all__ = [
    'BoundingBox',
    'CreateProjectOptions',
    'CustomBaseModelInfo',
    'CustomVisionError', 'CustomVisionErrorException',
    'Domain',
    'Export',
    'Image',
    'ImageCreateResult',
    'ImageCreateSummary',
    'ImageFileCreateBatch',
    'ImageFileCreateEntry',
    'ImageIdCreateBatch',
    'ImageIdCreateEntry',
    'ImageMetadataUpdateEntry',
    'ImageMetadataUpdateSummary',
    'ImagePerformance',
    'ImagePrediction',
    'ImageProcessingSettings',
    'ImageRegion',
    'ImageRegionCreateBatch',
    'ImageRegionCreateEntry',
    'ImageRegionCreateResult',
    'ImageRegionCreateSummary',
    'ImageRegionProposal',
    'ImageTag',
    'ImageTagCreateBatch',
    'ImageTagCreateEntry',
    'ImageTagCreateSummary',
    'ImageUrl',
    'ImageUrlCreateBatch',
    'ImageUrlCreateEntry',
    'Iteration',
    'IterationPerformance',
    'ModelInformation',
    'Prediction',
    'PredictionQueryResult',
    'PredictionQueryTag',
    'PredictionQueryToken',
    'Project',
    'ProjectExport',
    'ProjectSettings',
    'Region',
    'RegionProposal',
    'StoredImagePrediction',
    'StoredSuggestedTagAndRegion',
    'SuggestedTagAndRegion',
    'SuggestedTagAndRegionQuery',
    'SuggestedTagAndRegionQueryToken',
    'Tag',
    'TagFilter',
    'TagPerformance',
    'TrainingParameters',
    'CustomVisionErrorCodes',
    'DomainType',
    'ExportPlatform',
    'ExportStatus',
    'ExportFlavor',
    'ImageCreateStatus',
    'ImageMetadataUpdateStatus',
    'TagType',
    'Classifier',
    'TrainingType',
    'OrderBy',
    'ProjectStatus',
    'SortBy',
]
