# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
# --------------------------------------------------------------------------

from msrest.pipeline import ClientRawResponse
from msrest.exceptions import HttpOperationError
from .. import models


class CustomVisionTrainingClientOperationsMixin(object):

    def get_domains(
            self, custom_headers=None, raw=False, **operation_config):
        """Get a list of the available domains.

        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.Domain]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_domains.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True)
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[Domain]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_domains.metadata = {'url': '/domains'}

    def get_domain(
            self, domain_id, custom_headers=None, raw=False, **operation_config):
        """Get information about a specific domain.

        :param domain_id: The id of the domain to get information about.
        :type domain_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Domain or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Domain or
         ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_domain.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'domainId': self._serialize.url("domain_id", domain_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Domain', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_domain.metadata = {'url': '/domains/{domainId}'}

    def get_projects(
            self, custom_headers=None, raw=False, **operation_config):
        """Get your projects.

        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.Project]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_projects.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True)
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[Project]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_projects.metadata = {'url': '/projects'}

    def create_project(
            self, name, description=None, domain_id=None, classification_type=None, target_export_platforms=None, export_model_container_uri=None, notification_queue_uri=None, custom_headers=None, raw=False, **operation_config):
        """Create a project.

        :param name: Name of the project.
        :type name: str
        :param description: The description of the project.
        :type description: str
        :param domain_id: The id of the domain to use for this project.
         Defaults to General.
        :type domain_id: str
        :param classification_type: The type of classifier to create for this
         project. Possible values include: 'Multiclass', 'Multilabel'
        :type classification_type: str
        :param target_export_platforms: List of platforms the trained model is
         intending exporting to.
        :type target_export_platforms: list[str]
        :param export_model_container_uri: The uri to the Azure Storage
         container that will be used to store exported models.
        :type export_model_container_uri: str
        :param notification_queue_uri: The uri to the Azure Storage queue that
         will be used to send project-related notifications. See <a
         href="https://go.microsoft.com/fwlink/?linkid=2144149">Storage
         notifications</a> documentation for setup and message format.
        :type notification_queue_uri: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Project or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Project
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        options = None
        if export_model_container_uri is not None or notification_queue_uri is not None:
            options = models.CreateProjectOptions(export_model_container_uri=export_model_container_uri, notification_queue_uri=notification_queue_uri)

        # Construct URL
        url = self.create_project.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True)
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['name'] = self._serialize.query("name", name, 'str')
        if description is not None:
            query_parameters['description'] = self._serialize.query("description", description, 'str')
        if domain_id is not None:
            query_parameters['domainId'] = self._serialize.query("domain_id", domain_id, 'str')
        if classification_type is not None:
            query_parameters['classificationType'] = self._serialize.query("classification_type", classification_type, 'str')
        if target_export_platforms is not None:
            query_parameters['targetExportPlatforms'] = self._serialize.query("target_export_platforms", target_export_platforms, '[str]', div=',')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        if options is not None:
            body_content = self._serialize.body(options, 'CreateProjectOptions')
        else:
            body_content = None

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Project', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    create_project.metadata = {'url': '/projects'}

    def get_project(
            self, project_id, custom_headers=None, raw=False, **operation_config):
        """Get a specific project.

        :param project_id: The id of the project to get.
        :type project_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Project or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Project
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_project.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Project', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_project.metadata = {'url': '/projects/{projectId}'}

    def delete_project(
            self, project_id, custom_headers=None, raw=False, **operation_config):
        """Delete a specific project.

        :param project_id: The project id.
        :type project_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: None or ClientRawResponse if raw=true
        :rtype: None or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.delete_project.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.delete(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [204]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        if raw:
            client_raw_response = ClientRawResponse(None, response)
            return client_raw_response
    delete_project.metadata = {'url': '/projects/{projectId}'}

    def update_project(
            self, project_id, updated_project, custom_headers=None, raw=False, **operation_config):
        """Update a specific project.

        :param project_id: The id of the project to update.
        :type project_id: str
        :param updated_project: The updated project model.
        :type updated_project:
         ~azure.cognitiveservices.vision.customvision.training.models.Project
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Project or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Project
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.update_project.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(updated_project, 'Project')

        # Construct and send request
        request = self._client.patch(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Project', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    update_project.metadata = {'url': '/projects/{projectId}'}

    def get_artifact(
            self, project_id, path, custom_headers=None, raw=False, callback=None, **operation_config):
        """Get artifact content from blob storage, based on artifact relative path
        in the blob.

        :param project_id: The project id.
        :type project_id: str
        :param path: The relative path for artifact.
        :type path: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param callback: When specified, will be called with each chunk of
         data that is streamed. The callback should take two arguments, the
         bytes of the current chunk of data and the response object. If the
         data is uploading, response will be None.
        :type callback: Callable[Bytes, response=None]
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: object or ClientRawResponse if raw=true
        :rtype: Generator or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`HttpOperationError<msrest.exceptions.HttpOperationError>`
        """
        # Construct URL
        url = self.get_artifact.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['path'] = self._serialize.query("path", path, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/octet-stream'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=True, **operation_config)

        if response.status_code not in [200]:
            raise HttpOperationError(self._deserialize, response)

        deserialized = self._client.stream_download(response, callback)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_artifact.metadata = {'url': '/projects/{projectId}/artifacts'}

    def export_project(
            self, project_id, custom_headers=None, raw=False, **operation_config):
        """Exports a project.

        :param project_id: The project id of the project to export.
        :type project_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ProjectExport or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ProjectExport
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.export_project.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ProjectExport', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    export_project.metadata = {'url': '/projects/{projectId}/export'}

    def get_images(
            self, project_id, iteration_id=None, tag_ids=None, tagging_status=None, filter=None, order_by=None, take=50, skip=0, custom_headers=None, raw=False, **operation_config):
        """Get images for a given project iteration or workspace.

        This API supports batching and range selection. By default it will only
        return first 50 images matching images.
        Use the {take} and {skip} parameters to control how many images to
        return in a given batch.
        The filtering is on an and/or relationship. For example, if the
        provided tag ids are for the "Dog" and
        "Cat" tags, then only images tagged with Dog and/or Cat will be
        returned.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param tag_ids: A list of tags ids to filter the images. Defaults to
         all tagged images when null. Limited to 20.
        :type tag_ids: list[str]
        :param tagging_status: The tagging status filter. It can be 'All',
         'Tagged', or 'Untagged'. Defaults to 'All'. Possible values include:
         'All', 'Tagged', 'Untagged'
        :type tagging_status: str
        :param filter: An expression to filter the images against image
         metadata. Only images where the expression evaluates to true are
         included in the response.
         The expression supports eq (Equal), ne (Not equal), and (Logical and),
         or (Logical or) operators.
         Here is an example, metadata=key1 eq 'value1' and key2 ne 'value2'.
        :type filter: str
        :param order_by: The ordering. Defaults to newest. Possible values
         include: 'Newest', 'Oldest'
        :type order_by: str
        :param take: Maximum number of images to return. Defaults to 50,
         limited to 256.
        :type take: int
        :param skip: Number of images to skip before beginning the image
         batch. Defaults to 0.
        :type skip: int
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.Image]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_images.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')
        if tag_ids is not None:
            query_parameters['tagIds'] = self._serialize.query("tag_ids", tag_ids, '[str]', div=',', max_items=20, min_items=0)
        if tagging_status is not None:
            query_parameters['taggingStatus'] = self._serialize.query("tagging_status", tagging_status, 'str')
        if filter is not None:
            query_parameters['$filter'] = self._serialize.query("filter", filter, 'str')
        if order_by is not None:
            query_parameters['orderBy'] = self._serialize.query("order_by", order_by, 'str')
        if take is not None:
            query_parameters['take'] = self._serialize.query("take", take, 'int', maximum=256, minimum=0)
        if skip is not None:
            query_parameters['skip'] = self._serialize.query("skip", skip, 'int')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[Image]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_images.metadata = {'url': '/projects/{projectId}/images'}

    def create_images_from_data(
            self, project_id, image_data, tag_ids=None, custom_headers=None, raw=False, **operation_config):
        """Add the provided images to the set of training images.

        This API accepts body content as multipart/form-data and
        application/octet-stream. When using multipart
        multiple image files can be sent at once, with a maximum of 64 files.
        If all images are successful created, 200(OK) status code will be
        returned.
        Otherwise, 207 (Multi-Status) status code will be returned and detail
        status for each image will be listed in the response payload.

        :param project_id: The project id.
        :type project_id: str
        :param image_data: Binary image data. Supported formats are JPEG, GIF,
         PNG, and BMP. Supports images up to 6MB.
        :type image_data: Generator
        :param tag_ids: The tags ids with which to tag each image. Limited to
         20.
        :type tag_ids: list[str]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImageCreateSummary or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageCreateSummary
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.create_images_from_data.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if tag_ids is not None:
            query_parameters['tagIds'] = self._serialize.query("tag_ids", tag_ids, '[str]', div=',', max_items=20, min_items=0)

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'multipart/form-data'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct form data
        form_data_content = {
            'imageData': image_data,
        }

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, form_content=form_data_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200, 207]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImageCreateSummary', response)
        if response.status_code == 207:
            deserialized = self._deserialize('ImageCreateSummary', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    create_images_from_data.metadata = {'url': '/projects/{projectId}/images'}

    def delete_images(
            self, project_id, image_ids=None, all_images=None, all_iterations=None, custom_headers=None, raw=False, **operation_config):
        """Delete images from the set of training images.

        :param project_id: The project id.
        :type project_id: str
        :param image_ids: Ids of the images to be deleted. Limited to 256
         images per batch.
        :type image_ids: list[str]
        :param all_images: Flag to specify delete all images, specify this
         flag or a list of images. Using this flag will return a 202 response
         to indicate the images are being deleted.
        :type all_images: bool
        :param all_iterations: Removes these images from all iterations, not
         just the current workspace. Using this flag will return a 202 response
         to indicate the images are being deleted.
        :type all_iterations: bool
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: None or ClientRawResponse if raw=true
        :rtype: None or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.delete_images.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if image_ids is not None:
            query_parameters['imageIds'] = self._serialize.query("image_ids", image_ids, '[str]', div=',', max_items=256, min_items=0)
        if all_images is not None:
            query_parameters['allImages'] = self._serialize.query("all_images", all_images, 'bool')
        if all_iterations is not None:
            query_parameters['allIterations'] = self._serialize.query("all_iterations", all_iterations, 'bool')

        # Construct headers
        header_parameters = {}
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.delete(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [202, 204]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        if raw:
            client_raw_response = ClientRawResponse(None, response)
            return client_raw_response
    delete_images.metadata = {'url': '/projects/{projectId}/images'}

    def get_image_region_proposals(
            self, project_id, image_id, custom_headers=None, raw=False, **operation_config):
        """Get region proposals for an image. Returns empty array if no proposals
        are found.

        This API will get region proposals for an image along with confidences
        for the region. It returns an empty array if no proposals are found.

        :param project_id: The project id.
        :type project_id: str
        :param image_id: The image id.
        :type image_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImageRegionProposal or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageRegionProposal
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_image_region_proposals.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'imageId': self._serialize.url("image_id", image_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImageRegionProposal', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_image_region_proposals.metadata = {'url': '/projects/{projectId}/images/{imageId}/regionproposals'}

    def get_image_count(
            self, project_id, iteration_id=None, tagging_status=None, filter=None, tag_ids=None, custom_headers=None, raw=False, **operation_config):
        """Get the number of images.

        The filtering is on an and/or relationship. For example, if the
        provided tag ids are for the "Dog" and
        "Cat" tags, then only images tagged with Dog and/or Cat will be
        returned.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param tagging_status: The tagging status filter. It can be 'All',
         'Tagged', or 'Untagged'. Defaults to 'All'. Possible values include:
         'All', 'Tagged', 'Untagged'
        :type tagging_status: str
        :param filter: An expression to filter the images against image
         metadata. Only images where the expression evaluates to true are
         included in the response.
         The expression supports eq (Equal), ne (Not equal), and (Logical and),
         or (Logical or) operators.
         Here is an example, metadata=key1 eq 'value1' and key2 ne 'value2'.
        :type filter: str
        :param tag_ids: A list of tags ids to filter the images to count.
         Defaults to all tags when null.
        :type tag_ids: list[str]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: int or ClientRawResponse if raw=true
        :rtype: int or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_image_count.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')
        if tagging_status is not None:
            query_parameters['taggingStatus'] = self._serialize.query("tagging_status", tagging_status, 'str')
        if filter is not None:
            query_parameters['$filter'] = self._serialize.query("filter", filter, 'str')
        if tag_ids is not None:
            query_parameters['tagIds'] = self._serialize.query("tag_ids", tag_ids, '[str]', div=',')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('int', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_image_count.metadata = {'url': '/projects/{projectId}/images/count'}

    def create_images_from_files(
            self, project_id, batch, custom_headers=None, raw=False, **operation_config):
        """Add the provided batch of images to the set of training images.

        This API accepts a batch of files, and optionally tags, to create
        images. There is a limit of 64 images and 20 tags.
        If all images are successful created, 200(OK) status code will be
        returned.
        Otherwise, 207 (Multi-Status) status code will be returned and detail
        status for each image will be listed in the response payload.

        :param project_id: The project id.
        :type project_id: str
        :param batch: The batch of image files to add. Limited to 64 images
         and 20 tags per batch.
        :type batch:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageFileCreateBatch
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImageCreateSummary or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageCreateSummary
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.create_images_from_files.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(batch, 'ImageFileCreateBatch')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200, 207]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImageCreateSummary', response)
        if response.status_code == 207:
            deserialized = self._deserialize('ImageCreateSummary', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    create_images_from_files.metadata = {'url': '/projects/{projectId}/images/files'}

    def get_images_by_ids(
            self, project_id, image_ids=None, iteration_id=None, custom_headers=None, raw=False, **operation_config):
        """Get images by id for a given project iteration.

        This API will return a set of Images for the specified tags and
        optionally iteration. If no iteration is specified the
        current workspace is used.

        :param project_id: The project id.
        :type project_id: str
        :param image_ids: The list of image ids to retrieve. Limited to 256.
        :type image_ids: list[str]
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.Image]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_images_by_ids.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if image_ids is not None:
            query_parameters['imageIds'] = self._serialize.query("image_ids", image_ids, '[str]', div=',', max_items=256, min_items=0)
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[Image]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_images_by_ids.metadata = {'url': '/projects/{projectId}/images/id'}

    def update_image_metadata(
            self, project_id, image_ids, metadata, custom_headers=None, raw=False, **operation_config):
        """Update metadata of images.

        This API accepts a batch of image Ids, and metadata, to update images.
        There is a limit of 64 images.

        :param project_id: The project id.
        :type project_id: str
        :param image_ids: The list of image ids to update. Limited to 64.
        :type image_ids: list[str]
        :param metadata: The metadata to be updated to the specified images.
         Limited to 10 key-value pairs per image. The length of key is limited
         to 128. The length of value is limited to 256.
        :type metadata: dict[str, str]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImageMetadataUpdateSummary or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageMetadataUpdateSummary
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.update_image_metadata.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['imageIds'] = self._serialize.query("image_ids", image_ids, '[str]', div=',', max_items=256, min_items=0)

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(metadata, '{str}')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200, 207]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImageMetadataUpdateSummary', response)
        if response.status_code == 207:
            deserialized = self._deserialize('ImageMetadataUpdateSummary', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    update_image_metadata.metadata = {'url': '/projects/{projectId}/images/metadata'}

    def create_images_from_predictions(
            self, project_id, batch, custom_headers=None, raw=False, **operation_config):
        """Add the specified predicted images to the set of training images.

        This API creates a batch of images from predicted images specified.
        There is a limit of 64 images and 20 tags.
        If all images are successful created, 200(OK) status code will be
        returned.
        Otherwise, 207 (Multi-Status) status code will be returned and detail
        status for each image will be listed in the response payload.

        :param project_id: The project id.
        :type project_id: str
        :param batch: Image, tag ids, and metadata. Limited to 64 images and
         20 tags per batch.
        :type batch:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageIdCreateBatch
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImageCreateSummary or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageCreateSummary
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.create_images_from_predictions.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(batch, 'ImageIdCreateBatch')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200, 207]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImageCreateSummary', response)
        if response.status_code == 207:
            deserialized = self._deserialize('ImageCreateSummary', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    create_images_from_predictions.metadata = {'url': '/projects/{projectId}/images/predictions'}

    def create_image_regions(
            self, project_id, regions=None, custom_headers=None, raw=False, **operation_config):
        """Create a set of image regions.

        This API accepts a batch of image regions, and optionally tags, to
        update existing images with region information.
        There is a limit of 64 entries in the batch.
        If all regions are successful created, 200(OK) status code will be
        returned.
        Otherwise, 207 (Multi-Status) status code will be returned and detail
        status for each region will be listed in the response payload.

        :param project_id: The project id.
        :type project_id: str
        :param regions:
        :type regions:
         list[~azure.cognitiveservices.vision.customvision.training.models.ImageRegionCreateEntry]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImageRegionCreateSummary or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageRegionCreateSummary
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        batch = models.ImageRegionCreateBatch(regions=regions)

        # Construct URL
        url = self.create_image_regions.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(batch, 'ImageRegionCreateBatch')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200, 207]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImageRegionCreateSummary', response)
        if response.status_code == 207:
            deserialized = self._deserialize('ImageRegionCreateSummary', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    create_image_regions.metadata = {'url': '/projects/{projectId}/images/regions'}

    def delete_image_regions(
            self, project_id, region_ids, custom_headers=None, raw=False, **operation_config):
        """Delete a set of image regions.

        :param project_id: The project id.
        :type project_id: str
        :param region_ids: Regions to delete. Limited to 64.
        :type region_ids: list[str]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: None or ClientRawResponse if raw=true
        :rtype: None or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.delete_image_regions.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['regionIds'] = self._serialize.query("region_ids", region_ids, '[str]', div=',', max_items=64, min_items=0)

        # Construct headers
        header_parameters = {}
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.delete(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [204]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        if raw:
            client_raw_response = ClientRawResponse(None, response)
            return client_raw_response
    delete_image_regions.metadata = {'url': '/projects/{projectId}/images/regions'}

    def query_suggested_images(
            self, project_id, iteration_id, query, custom_headers=None, raw=False, **operation_config):
        """Get untagged images whose suggested tags match given tags. Returns
        empty array if no images are found.

        This API will fetch untagged images filtered by suggested tags Ids. It
        returns an empty array if no images are found.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: IterationId to use for the suggested tags and
         regions.
        :type iteration_id: str
        :param query: Contains properties we need to query suggested images.
        :type query:
         ~azure.cognitiveservices.vision.customvision.training.models.SuggestedTagAndRegionQueryToken
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: SuggestedTagAndRegionQuery or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.SuggestedTagAndRegionQuery
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.query_suggested_images.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(query, 'SuggestedTagAndRegionQueryToken')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('SuggestedTagAndRegionQuery', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    query_suggested_images.metadata = {'url': '/projects/{projectId}/images/suggested'}

    def query_suggested_image_count(
            self, project_id, iteration_id, tag_ids=None, threshold=None, custom_headers=None, raw=False, **operation_config):
        """Get count of images whose suggested tags match given tags and their
        probabilities are greater than or equal to the given threshold. Returns
        count as 0 if none found.

        This API takes in tagIds to get count of untagged images per suggested
        tags for a given threshold.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: IterationId to use for the suggested tags and
         regions.
        :type iteration_id: str
        :param tag_ids: Existing TagIds in project to get suggested tags count
         for.
        :type tag_ids: list[str]
        :param threshold: Confidence threshold to filter suggested tags on.
        :type threshold: float
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: dict or ClientRawResponse if raw=true
        :rtype: dict[str, int] or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        query = models.TagFilter(tag_ids=tag_ids, threshold=threshold)

        # Construct URL
        url = self.query_suggested_image_count.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(query, 'TagFilter')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('{int}', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    query_suggested_image_count.metadata = {'url': '/projects/{projectId}/images/suggested/count'}

    def get_tagged_images(
            self, project_id, iteration_id=None, tag_ids=None, order_by=None, take=50, skip=0, custom_headers=None, raw=False, **operation_config):
        """Get tagged images for a given project iteration.

        This API supports batching and range selection. By default it will only
        return first 50 images matching images.
        Use the {take} and {skip} parameters to control how many images to
        return in a given batch.
        The filtering is on an and/or relationship. For example, if the
        provided tag ids are for the "Dog" and
        "Cat" tags, then only images tagged with Dog and/or Cat will be
        returned.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param tag_ids: A list of tags ids to filter the images. Defaults to
         all tagged images when null. Limited to 20.
        :type tag_ids: list[str]
        :param order_by: The ordering. Defaults to newest. Possible values
         include: 'Newest', 'Oldest'
        :type order_by: str
        :param take: Maximum number of images to return. Defaults to 50,
         limited to 256.
        :type take: int
        :param skip: Number of images to skip before beginning the image
         batch. Defaults to 0.
        :type skip: int
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.Image]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_tagged_images.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')
        if tag_ids is not None:
            query_parameters['tagIds'] = self._serialize.query("tag_ids", tag_ids, '[str]', div=',', max_items=20, min_items=0)
        if order_by is not None:
            query_parameters['orderBy'] = self._serialize.query("order_by", order_by, 'str')
        if take is not None:
            query_parameters['take'] = self._serialize.query("take", take, 'int', maximum=256, minimum=0)
        if skip is not None:
            query_parameters['skip'] = self._serialize.query("skip", skip, 'int')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[Image]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_tagged_images.metadata = {'url': '/projects/{projectId}/images/tagged'}

    def get_tagged_image_count(
            self, project_id, iteration_id=None, tag_ids=None, custom_headers=None, raw=False, **operation_config):
        """Gets the number of images tagged with the provided {tagIds}.

        The filtering is on an and/or relationship. For example, if the
        provided tag ids are for the "Dog" and
        "Cat" tags, then only images tagged with Dog and/or Cat will be
        returned.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param tag_ids: A list of tags ids to filter the images to count.
         Defaults to all tags when null.
        :type tag_ids: list[str]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: int or ClientRawResponse if raw=true
        :rtype: int or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_tagged_image_count.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')
        if tag_ids is not None:
            query_parameters['tagIds'] = self._serialize.query("tag_ids", tag_ids, '[str]', div=',')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('int', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_tagged_image_count.metadata = {'url': '/projects/{projectId}/images/tagged/count'}

    def create_image_tags(
            self, project_id, tags=None, custom_headers=None, raw=False, **operation_config):
        """Associate a set of images with a set of tags.

        :param project_id: The project id.
        :type project_id: str
        :param tags: Image Tag entries to include in this batch.
        :type tags:
         list[~azure.cognitiveservices.vision.customvision.training.models.ImageTagCreateEntry]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImageTagCreateSummary or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageTagCreateSummary
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        batch = models.ImageTagCreateBatch(tags=tags)

        # Construct URL
        url = self.create_image_tags.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(batch, 'ImageTagCreateBatch')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImageTagCreateSummary', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    create_image_tags.metadata = {'url': '/projects/{projectId}/images/tags'}

    def delete_image_tags(
            self, project_id, image_ids, tag_ids, custom_headers=None, raw=False, **operation_config):
        """Remove a set of tags from a set of images.

        :param project_id: The project id.
        :type project_id: str
        :param image_ids: Image ids. Limited to 64 images.
        :type image_ids: list[str]
        :param tag_ids: Tags to be deleted from the specified images. Limited
         to 20 tags.
        :type tag_ids: list[str]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: None or ClientRawResponse if raw=true
        :rtype: None or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.delete_image_tags.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['imageIds'] = self._serialize.query("image_ids", image_ids, '[str]', div=',', max_items=64, min_items=0)
        query_parameters['tagIds'] = self._serialize.query("tag_ids", tag_ids, '[str]', div=',', max_items=20, min_items=0)

        # Construct headers
        header_parameters = {}
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.delete(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [204]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        if raw:
            client_raw_response = ClientRawResponse(None, response)
            return client_raw_response
    delete_image_tags.metadata = {'url': '/projects/{projectId}/images/tags'}

    def get_untagged_images(
            self, project_id, iteration_id=None, order_by=None, take=50, skip=0, custom_headers=None, raw=False, **operation_config):
        """Get untagged images for a given project iteration.

        This API supports batching and range selection. By default it will only
        return first 50 images matching images.
        Use the {take} and {skip} parameters to control how many images to
        return in a given batch.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param order_by: The ordering. Defaults to newest. Possible values
         include: 'Newest', 'Oldest'
        :type order_by: str
        :param take: Maximum number of images to return. Defaults to 50,
         limited to 256.
        :type take: int
        :param skip: Number of images to skip before beginning the image
         batch. Defaults to 0.
        :type skip: int
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.Image]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_untagged_images.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')
        if order_by is not None:
            query_parameters['orderBy'] = self._serialize.query("order_by", order_by, 'str')
        if take is not None:
            query_parameters['take'] = self._serialize.query("take", take, 'int', maximum=256, minimum=0)
        if skip is not None:
            query_parameters['skip'] = self._serialize.query("skip", skip, 'int')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[Image]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_untagged_images.metadata = {'url': '/projects/{projectId}/images/untagged'}

    def get_untagged_image_count(
            self, project_id, iteration_id=None, custom_headers=None, raw=False, **operation_config):
        """Gets the number of untagged images.

        This API returns the images which have no tags for a given project and
        optionally an iteration. If no iteration is specified the
        current workspace is used.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: int or ClientRawResponse if raw=true
        :rtype: int or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_untagged_image_count.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('int', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_untagged_image_count.metadata = {'url': '/projects/{projectId}/images/untagged/count'}

    def create_images_from_urls(
            self, project_id, batch, custom_headers=None, raw=False, **operation_config):
        """Add the provided images urls to the set of training images.

        This API accepts a batch of urls, and optionally tags, to create
        images. There is a limit of 64 images and 20 tags.
        If all images are successful created, 200(OK) status code will be
        returned.
        Otherwise, 207 (Multi-Status) status code will be returned and detail
        status for each image will be listed in the response payload.

        :param project_id: The project id.
        :type project_id: str
        :param batch: Image urls, tag ids, and metadata. Limited to 64 images
         and 20 tags per batch.
        :type batch:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageUrlCreateBatch
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImageCreateSummary or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImageCreateSummary
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.create_images_from_urls.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(batch, 'ImageUrlCreateBatch')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200, 207]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImageCreateSummary', response)
        if response.status_code == 207:
            deserialized = self._deserialize('ImageCreateSummary', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    create_images_from_urls.metadata = {'url': '/projects/{projectId}/images/urls'}

    def get_iterations(
            self, project_id, custom_headers=None, raw=False, **operation_config):
        """Get iterations for the project.

        :param project_id: The project id.
        :type project_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.Iteration]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_iterations.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[Iteration]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_iterations.metadata = {'url': '/projects/{projectId}/iterations'}

    def get_iteration(
            self, project_id, iteration_id, custom_headers=None, raw=False, **operation_config):
        """Get a specific iteration.

        :param project_id: The id of the project the iteration belongs to.
        :type project_id: str
        :param iteration_id: The id of the iteration to get.
        :type iteration_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Iteration or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Iteration
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_iteration.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Iteration', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_iteration.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}'}

    def delete_iteration(
            self, project_id, iteration_id, custom_headers=None, raw=False, **operation_config):
        """Delete a specific iteration of a project.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id.
        :type iteration_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: None or ClientRawResponse if raw=true
        :rtype: None or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.delete_iteration.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.delete(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [204]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        if raw:
            client_raw_response = ClientRawResponse(None, response)
            return client_raw_response
    delete_iteration.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}'}

    def update_iteration(
            self, project_id, iteration_id, name, custom_headers=None, raw=False, **operation_config):
        """Update a specific iteration.

        :param project_id: Project id.
        :type project_id: str
        :param iteration_id: Iteration id.
        :type iteration_id: str
        :param name: Gets or sets the name of the iteration.
        :type name: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Iteration or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Iteration
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        updated_iteration = models.Iteration(name=name)

        # Construct URL
        url = self.update_iteration.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(updated_iteration, 'Iteration')

        # Construct and send request
        request = self._client.patch(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Iteration', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    update_iteration.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}'}

    def get_exports(
            self, project_id, iteration_id, custom_headers=None, raw=False, **operation_config):
        """Get the list of exports for a specific iteration.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id.
        :type iteration_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.Export]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_exports.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[Export]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_exports.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}/export'}

    def export_iteration(
            self, project_id, iteration_id, platform, flavor=None, custom_headers=None, raw=False, **operation_config):
        """Export a trained iteration.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id.
        :type iteration_id: str
        :param platform: The target platform. Possible values include:
         'CoreML', 'TensorFlow', 'DockerFile', 'ONNX', 'VAIDK', 'OpenVino'
        :type platform: str
        :param flavor: The flavor of the target platform. Possible values
         include: 'Linux', 'Windows', 'ONNX10', 'ONNX12', 'ARM',
         'TensorFlowNormal', 'TensorFlowLite'
        :type flavor: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Export or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Export or
         ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.export_iteration.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['platform'] = self._serialize.query("platform", platform, 'str')
        if flavor is not None:
            query_parameters['flavor'] = self._serialize.query("flavor", flavor, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Export', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    export_iteration.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}/export'}

    def get_iteration_performance(
            self, project_id, iteration_id, threshold=None, overlap_threshold=None, custom_headers=None, raw=False, **operation_config):
        """Get detailed performance information about an iteration.

        :param project_id: The id of the project the iteration belongs to.
        :type project_id: str
        :param iteration_id: The id of the iteration to get.
        :type iteration_id: str
        :param threshold: The threshold used to determine true predictions.
        :type threshold: float
        :param overlap_threshold: If applicable, the bounding box overlap
         threshold used to determine true predictions.
        :type overlap_threshold: float
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: IterationPerformance or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.IterationPerformance
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_iteration_performance.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if threshold is not None:
            query_parameters['threshold'] = self._serialize.query("threshold", threshold, 'float')
        if overlap_threshold is not None:
            query_parameters['overlapThreshold'] = self._serialize.query("overlap_threshold", overlap_threshold, 'float')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('IterationPerformance', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_iteration_performance.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}/performance'}

    def get_image_performances(
            self, project_id, iteration_id, tag_ids=None, order_by=None, take=50, skip=0, custom_headers=None, raw=False, **operation_config):
        """Get image with its prediction for a given project iteration.

        This API supports batching and range selection. By default it will only
        return first 50 images matching images.
        Use the {take} and {skip} parameters to control how many images to
        return in a given batch.
        The filtering is on an and/or relationship. For example, if the
        provided tag ids are for the "Dog" and
        "Cat" tags, then only images tagged with Dog and/or Cat will be
        returned.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param tag_ids: A list of tags ids to filter the images. Defaults to
         all tagged images when null. Limited to 20.
        :type tag_ids: list[str]
        :param order_by: The ordering. Defaults to newest. Possible values
         include: 'Newest', 'Oldest'
        :type order_by: str
        :param take: Maximum number of images to return. Defaults to 50,
         limited to 256.
        :type take: int
        :param skip: Number of images to skip before beginning the image
         batch. Defaults to 0.
        :type skip: int
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.ImagePerformance]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_image_performances.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if tag_ids is not None:
            query_parameters['tagIds'] = self._serialize.query("tag_ids", tag_ids, '[str]', div=',', max_items=20, min_items=0)
        if order_by is not None:
            query_parameters['orderBy'] = self._serialize.query("order_by", order_by, 'str')
        if take is not None:
            query_parameters['take'] = self._serialize.query("take", take, 'int', maximum=256, minimum=0)
        if skip is not None:
            query_parameters['skip'] = self._serialize.query("skip", skip, 'int')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[ImagePerformance]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_image_performances.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}/performance/images'}

    def get_image_performance_count(
            self, project_id, iteration_id, tag_ids=None, custom_headers=None, raw=False, **operation_config):
        """Gets the number of images tagged with the provided {tagIds} that have
        prediction results from
        training for the provided iteration {iterationId}.

        The filtering is on an and/or relationship. For example, if the
        provided tag ids are for the "Dog" and
        "Cat" tags, then only images tagged with Dog and/or Cat will be
        returned.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param tag_ids: A list of tags ids to filter the images to count.
         Defaults to all tags when null.
        :type tag_ids: list[str]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: int or ClientRawResponse if raw=true
        :rtype: int or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_image_performance_count.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if tag_ids is not None:
            query_parameters['tagIds'] = self._serialize.query("tag_ids", tag_ids, '[str]', div=',')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('int', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_image_performance_count.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}/performance/images/count'}

    def publish_iteration(
            self, project_id, iteration_id, publish_name, prediction_id, overwrite=None, custom_headers=None, raw=False, **operation_config):
        """Publish a specific iteration.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id.
        :type iteration_id: str
        :param publish_name: The name to give the published iteration.
        :type publish_name: str
        :param prediction_id: The id of the prediction resource to publish to.
        :type prediction_id: str
        :param overwrite: Whether to overwrite the published model with the
         given name (default: false).
        :type overwrite: bool
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: bool or ClientRawResponse if raw=true
        :rtype: bool or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.publish_iteration.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['publishName'] = self._serialize.query("publish_name", publish_name, 'str')
        query_parameters['predictionId'] = self._serialize.query("prediction_id", prediction_id, 'str')
        if overwrite is not None:
            query_parameters['overwrite'] = self._serialize.query("overwrite", overwrite, 'bool')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('bool', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    publish_iteration.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}/publish'}

    def unpublish_iteration(
            self, project_id, iteration_id, custom_headers=None, raw=False, **operation_config):
        """Unpublish a specific iteration.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id.
        :type iteration_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: None or ClientRawResponse if raw=true
        :rtype: None or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.unpublish_iteration.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'iterationId': self._serialize.url("iteration_id", iteration_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.delete(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [204]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        if raw:
            client_raw_response = ClientRawResponse(None, response)
            return client_raw_response
    unpublish_iteration.metadata = {'url': '/projects/{projectId}/iterations/{iterationId}/publish'}

    def delete_prediction(
            self, project_id, ids, custom_headers=None, raw=False, **operation_config):
        """Delete a set of predicted images and their associated prediction
        results.

        :param project_id: The project id.
        :type project_id: str
        :param ids: The prediction ids. Limited to 64.
        :type ids: list[str]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: None or ClientRawResponse if raw=true
        :rtype: None or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.delete_prediction.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['ids'] = self._serialize.query("ids", ids, '[str]', div=',', max_items=64, min_items=0)

        # Construct headers
        header_parameters = {}
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.delete(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [204]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        if raw:
            client_raw_response = ClientRawResponse(None, response)
            return client_raw_response
    delete_prediction.metadata = {'url': '/projects/{projectId}/predictions'}

    def query_predictions(
            self, project_id, query, custom_headers=None, raw=False, **operation_config):
        """Get images that were sent to your prediction endpoint.

        :param project_id: The project id.
        :type project_id: str
        :param query: Parameters used to query the predictions. Limited to
         combining 2 tags.
        :type query:
         ~azure.cognitiveservices.vision.customvision.training.models.PredictionQueryToken
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: PredictionQueryResult or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.PredictionQueryResult
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.query_predictions.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(query, 'PredictionQueryToken')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('PredictionQueryResult', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    query_predictions.metadata = {'url': '/projects/{projectId}/predictions/query'}

    def quick_test_image(
            self, project_id, image_data, iteration_id=None, store=True, custom_headers=None, raw=False, **operation_config):
        """Quick test an image.

        :param project_id: The project id.
        :type project_id: str
        :param image_data: Binary image data. Supported formats are JPEG, GIF,
         PNG, and BMP. Supports images up to 6MB.
        :type image_data: Generator
        :param iteration_id: Optional. Specifies the id of a particular
         iteration to evaluate against.
         The default iteration for the project will be used when not specified.
        :type iteration_id: str
        :param store: Optional. Specifies whether or not to store the result
         of this prediction. The default is true, to store.
        :type store: bool
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.quick_test_image.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')
        if store is not None:
            query_parameters['store'] = self._serialize.query("store", store, 'bool')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'multipart/form-data'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct form data
        form_data_content = {
            'imageData': image_data,
        }

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, form_content=form_data_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    quick_test_image.metadata = {'url': '/projects/{projectId}/quicktest/image'}

    def quick_test_image_url(
            self, project_id, url, iteration_id=None, store=True, custom_headers=None, raw=False, **operation_config):
        """Quick test an image url.

        :param project_id: The project to evaluate against.
        :type project_id: str
        :param url: Url of the image.
        :type url: str
        :param iteration_id: Optional. Specifies the id of a particular
         iteration to evaluate against.
         The default iteration for the project will be used when not specified.
        :type iteration_id: str
        :param store: Optional. Specifies whether or not to store the result
         of this prediction. The default is true, to store.
        :type store: bool
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: ImagePrediction or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.ImagePrediction
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        image_url = models.ImageUrl(url=url)

        # Construct URL
        url = self.quick_test_image_url.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')
        if store is not None:
            query_parameters['store'] = self._serialize.query("store", store, 'bool')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(image_url, 'ImageUrl')

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('ImagePrediction', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    quick_test_image_url.metadata = {'url': '/projects/{projectId}/quicktest/url'}

    def get_tags(
            self, project_id, iteration_id=None, custom_headers=None, raw=False, **operation_config):
        """Get the tags for a given project and iteration.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: The iteration id. Defaults to workspace.
        :type iteration_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.Tag]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_tags.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[Tag]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_tags.metadata = {'url': '/projects/{projectId}/tags'}

    def create_tag(
            self, project_id, name, description=None, type=None, custom_headers=None, raw=False, **operation_config):
        """Create a tag for the project.

        :param project_id: The project id.
        :type project_id: str
        :param name: The tag name.
        :type name: str
        :param description: Optional description for the tag.
        :type description: str
        :param type: Optional type for the tag. Possible values include:
         'Regular', 'Negative', 'GeneralProduct'
        :type type: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Tag or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Tag or
         ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.create_tag.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['name'] = self._serialize.query("name", name, 'str')
        if description is not None:
            query_parameters['description'] = self._serialize.query("description", description, 'str')
        if type is not None:
            query_parameters['type'] = self._serialize.query("type", type, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Tag', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    create_tag.metadata = {'url': '/projects/{projectId}/tags'}

    def get_tag(
            self, project_id, tag_id, iteration_id=None, custom_headers=None, raw=False, **operation_config):
        """Get information about a specific tag.

        :param project_id: The project this tag belongs to.
        :type project_id: str
        :param tag_id: The tag id.
        :type tag_id: str
        :param iteration_id: The iteration to retrieve this tag from.
         Optional, defaults to current training set.
        :type iteration_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Tag or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Tag or
         ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.get_tag.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'tagId': self._serialize.url("tag_id", tag_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if iteration_id is not None:
            query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.get(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Tag', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    get_tag.metadata = {'url': '/projects/{projectId}/tags/{tagId}'}

    def delete_tag(
            self, project_id, tag_id, custom_headers=None, raw=False, **operation_config):
        """Delete a tag from the project.

        :param project_id: The project id.
        :type project_id: str
        :param tag_id: Id of the tag to be deleted.
        :type tag_id: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: None or ClientRawResponse if raw=true
        :rtype: None or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.delete_tag.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'tagId': self._serialize.url("tag_id", tag_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.delete(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [204]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        if raw:
            client_raw_response = ClientRawResponse(None, response)
            return client_raw_response
    delete_tag.metadata = {'url': '/projects/{projectId}/tags/{tagId}'}

    def update_tag(
            self, project_id, tag_id, updated_tag, custom_headers=None, raw=False, **operation_config):
        """Update a tag.

        :param project_id: The project id.
        :type project_id: str
        :param tag_id: The id of the target tag.
        :type tag_id: str
        :param updated_tag: The updated tag model.
        :type updated_tag:
         ~azure.cognitiveservices.vision.customvision.training.models.Tag
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Tag or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Tag or
         ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.update_tag.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str'),
            'tagId': self._serialize.url("tag_id", tag_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        body_content = self._serialize.body(updated_tag, 'Tag')

        # Construct and send request
        request = self._client.patch(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Tag', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    update_tag.metadata = {'url': '/projects/{projectId}/tags/{tagId}'}

    def suggest_tags_and_regions(
            self, project_id, iteration_id, image_ids, custom_headers=None, raw=False, **operation_config):
        """Suggest tags and regions for an array/batch of untagged images. Returns
        empty array if no tags are found.

        This API will get suggested tags and regions for an array/batch of
        untagged images along with confidences for the tags. It returns an
        empty array if no tags are found.
        There is a limit of 64 images in the batch.

        :param project_id: The project id.
        :type project_id: str
        :param iteration_id: IterationId to use for tag and region suggestion.
        :type iteration_id: str
        :param image_ids: Array of image ids tag suggestion are needed for.
         Use GetUntaggedImages API to get imageIds.
        :type image_ids: list[str]
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: list or ClientRawResponse if raw=true
        :rtype:
         list[~azure.cognitiveservices.vision.customvision.training.models.SuggestedTagAndRegion]
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.suggest_tags_and_regions.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['iterationId'] = self._serialize.query("iteration_id", iteration_id, 'str')
        query_parameters['imageIds'] = self._serialize.query("image_ids", image_ids, '[str]', div=',', max_items=64, min_items=0)

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('[SuggestedTagAndRegion]', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    suggest_tags_and_regions.metadata = {'url': '/projects/{projectId}/tagsandregions/suggestions'}

    def train_project(
            self, project_id, training_type=None, reserved_budget_in_hours=0, force_train=False, notification_email_address=None, selected_tags=None, custom_base_model_info=None, custom_headers=None, raw=False, **operation_config):
        """Queues project for training.

        :param project_id: The project id.
        :type project_id: str
        :param training_type: The type of training to use to train the project
         (default: Regular). Possible values include: 'Regular', 'Advanced'
        :type training_type: str
        :param reserved_budget_in_hours: The number of hours reserved as
         budget for training (if applicable).
        :type reserved_budget_in_hours: int
        :param force_train: Whether to force train even if dataset and
         configuration does not change (default: false).
        :type force_train: bool
        :param notification_email_address: The email address to send
         notification to when training finishes (default: null).
        :type notification_email_address: str
        :param selected_tags: List of tags selected for this training session,
         other tags in the project will be ignored.
        :type selected_tags: list[str]
        :param custom_base_model_info: Information of the previously trained
         iteration which provides the base model for current iteration's
         training.
        :type custom_base_model_info:
         ~azure.cognitiveservices.vision.customvision.training.models.CustomBaseModelInfo
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Iteration or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Iteration
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        training_parameters = None
        if selected_tags is not None or custom_base_model_info is not None:
            training_parameters = models.TrainingParameters(selected_tags=selected_tags, custom_base_model_info=custom_base_model_info)

        # Construct URL
        url = self.train_project.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True),
            'projectId': self._serialize.url("project_id", project_id, 'str')
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        if training_type is not None:
            query_parameters['trainingType'] = self._serialize.query("training_type", training_type, 'str')
        if reserved_budget_in_hours is not None:
            query_parameters['reservedBudgetInHours'] = self._serialize.query("reserved_budget_in_hours", reserved_budget_in_hours, 'int')
        if force_train is not None:
            query_parameters['forceTrain'] = self._serialize.query("force_train", force_train, 'bool')
        if notification_email_address is not None:
            query_parameters['notificationEmailAddress'] = self._serialize.query("notification_email_address", notification_email_address, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        header_parameters['Content-Type'] = 'application/json; charset=utf-8'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct body
        if training_parameters is not None:
            body_content = self._serialize.body(training_parameters, 'TrainingParameters')
        else:
            body_content = None

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters, body_content)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Iteration', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    train_project.metadata = {'url': '/projects/{projectId}/train'}

    def import_project(
            self, token, name=None, custom_headers=None, raw=False, **operation_config):
        """Imports a project.

        :param token: Token generated from the export project call.
        :type token: str
        :param name: Optional, name of the project to use instead of
         auto-generated name.
        :type name: str
        :param dict custom_headers: headers that will be added to the request
        :param bool raw: returns the direct response alongside the
         deserialized response
        :param operation_config: :ref:`Operation configuration
         overrides<msrest:optionsforoperations>`.
        :return: Project or ClientRawResponse if raw=true
        :rtype:
         ~azure.cognitiveservices.vision.customvision.training.models.Project
         or ~msrest.pipeline.ClientRawResponse
        :raises:
         :class:`CustomVisionErrorException<azure.cognitiveservices.vision.customvision.training.models.CustomVisionErrorException>`
        """
        # Construct URL
        url = self.import_project.metadata['url']
        path_format_arguments = {
            'Endpoint': self._serialize.url("self.config.endpoint", self.config.endpoint, 'str', skip_quote=True)
        }
        url = self._client.format_url(url, **path_format_arguments)

        # Construct parameters
        query_parameters = {}
        query_parameters['token'] = self._serialize.query("token", token, 'str')
        if name is not None:
            query_parameters['name'] = self._serialize.query("name", name, 'str')

        # Construct headers
        header_parameters = {}
        header_parameters['Accept'] = 'application/json'
        if custom_headers:
            header_parameters.update(custom_headers)

        # Construct and send request
        request = self._client.post(url, query_parameters, header_parameters)
        response = self._client.send(request, stream=False, **operation_config)

        if response.status_code not in [200]:
            raise models.CustomVisionErrorException(self._deserialize, response)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize('Project', response)

        if raw:
            client_raw_response = ClientRawResponse(deserialized, response)
            return client_raw_response

        return deserialized
    import_project.metadata = {'url': '/projects/import'}
