Metadata-Version: 2.1
Name: azure-mgmt-core
Version: 1.6.0
Summary: Microsoft Azure Management Core Library for Python
Home-page: https://github.com/Azure/azure-sdk-for-python/tree/main/sdk/core/azure-mgmt-core
Author: Microsoft Corporation
Author-email: azpysd<PERSON><PERSON><PERSON>@microsoft.com
License: MIT License
Keywords: azure,azure sdk
Classifier: Development Status :: 5 - Production/Stable
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: License :: OSI Approved :: MIT License
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: LICENSE.md
Requires-Dist: azure-core>=1.32.0


# Azure Management Core Library

Azure management core library defines extensions to Azure Core that are specific to ARM (Azure Resource Management) needed when you use client libraries.

As an end user, you don't need to manually install azure-mgmt-core because it will be installed automatically when you install other SDKs.

[Source code](https://github.com/Azure/azure-sdk-for-python/blob/main/sdk/core/azure-mgmt-core/) | [Package (Pypi)][package] | [API reference documentation](https://github.com/Azure/azure-sdk-for-python/blob/main/sdk/core/azure-mgmt-core/)


## Contributing
This project welcomes contributions and suggestions. Most contributions require
you to agree to a Contributor License Agreement (CLA) declaring that you have
the right to, and actually do, grant us the rights to use your contribution.
For details, visit [https://cla.microsoft.com](https://cla.microsoft.com).

When you submit a pull request, a CLA-bot will automatically determine whether
you need to provide a CLA and decorate the PR appropriately (e.g., label,
comment). Simply follow the instructions provided by the bot. You will only
need to do this once across all repos using our CLA.

This project has adopted the
[Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/).
For more information, see the
[Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/)
or contact [<EMAIL>](mailto:<EMAIL>) with any
additional questions or comments.

<!-- LINKS -->
[package]: https://pypi.org/project/azure-mgmt-core/

# Release History

## 1.6.0 (2025-07-02)

### Other Changes

- `ARMChallengeAuthenticationPolicy` adopt `on_challenge` in `BearerTokenCredentialPolicy` of `azure-core` to support complete CAE challenges.
- Python 3.8 is no longer supported. Please use Python version 3.9 or later.

## 1.5.0 (2024-10-31)

### Features Added

- Added helper function `get_arm_endpoints` to get the ARM endpoint and credential scopes from the cloud setting.

## 1.4.0 (2023-04-06)

### Features

- Added AuxiliaryAuthenticationPolicy

### Other Changes

- Rename "DEFAULT_HEADERS_WHITELIST" to "DEFAULT_HEADERS_ALLOWLIST". Added a backward compatible alias.

## 1.3.2 (2022-08-11)

### Other Changes

- Updated mindep about `azure-core` from `1.23.0` to `1.24.0`

## 1.3.1 (2022-06-14)

### Other Changes

- Updated mindep about `azure-core` from `1.15.0` to `1.23.0`

## 1.3.0 (2021-07-01)

### Features

- Support CAE

## 1.3.0b3 (2021-06-07)

### Changed

- Updated required `azure-core` version

## 1.3.0b2 (2021-05-13)

### Changed

- Updated required `azure-core` version

## 1.3.0b1 (2021-03-10)

### Features

- ARMChallengeAuthenticationPolicy supports bearer token authorization and CAE challenges

## 1.2.2 (2020-11-09)

### Bug Fixes

- Fixed bug to allow polling for PATCH long-running-operation.

## 1.2.1 (2020-10-05)

### Bug Fixes

- Fixed bug to allow polling in the case of parameterized endpoints with relative polling urls  #14097

## 1.2.0 (2020-07-06)

### Bug Fixes

- The `allowed_header_names` property of ARMHttpLoggingPolicy now includes the management plane specific
allowed headers  #12218

### Features

- Added `http_logging_policy` property on the `Configuration` object, allowing users to individually
set the http logging policy of the config  #12218

## 1.1.0 (2020-05-04)

### Features

- Info logger now logs ARM throttling information  #10940


## 1.0.0 (2020-04-09)

### Features

- Internal refactoring of polling on top of azure-core 1.4.0

## 1.0.0b1 (2020-03-10)

- Preview 1 release
